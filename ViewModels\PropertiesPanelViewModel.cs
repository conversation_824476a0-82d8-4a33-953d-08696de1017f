using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;
using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class PropertiesPanelViewModel : ObservableObject
{
    [ObservableProperty]
    private string selectedObjectName = "No Selection";

    [ObservableProperty]
    private string selectedObjectType = "";

    public ObservableCollection<PropertyItem> Properties { get; } = [];

    private NavigationNode? _currentNode;

    public void LoadProperties(NavigationNode node)
    {
        _currentNode = node;
        SelectedObjectName = node.Name;
        SelectedObjectType = node.NodeType.ToString();

        Properties.Clear();

        if (node.Data != null)
        {
            LoadPropertiesFromObject(node.Data);
        }
    }

    public void InitializeProperty(PropertyItem propertyItem)
    {
        if (propertyItem.SourceObject == null || string.IsNullOrEmpty(propertyItem.PropertyPath))
            return;

        try
        {
            // Get the property info
            var pathParts = propertyItem.PropertyPath.Split('.');
            var sourceType = propertyItem.SourceObject.GetType();
            var propertyInfo = sourceType.GetProperty(pathParts[0]);

            if (propertyInfo != null && propertyInfo.CanWrite)
            {
                // Create a default instance of the property type
                var defaultInstance = CreateDefaultInstance(propertyInfo.PropertyType);
                if (defaultInstance != null)
                {
                    propertyInfo.SetValue(propertyItem.SourceObject, defaultInstance);

                    // Refresh the properties display
                    if (_currentNode != null)
                    {
                        LoadProperties(_currentNode);
                    }
                }
                else
                {
                    // If we can't create a default instance, try a simpler approach
                    // Just create an empty instance without trying to set defaults
                    var simpleInstance = Activator.CreateInstance(propertyInfo.PropertyType);
                    if (simpleInstance != null)
                    {
                        propertyInfo.SetValue(propertyItem.SourceObject, simpleInstance);

                        // Refresh the properties display
                        if (_currentNode != null)
                        {
                            LoadProperties(_currentNode);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // For debugging - in a real app you'd log this properly
            System.Diagnostics.Debug.WriteLine($"Failed to initialize property {propertyItem.Name}: {ex.Message}");
        }
    }

    private object? CreateDefaultInstance(Type type)
    {
        try
        {
            // Create the main instance
            var instance = Activator.CreateInstance(type);
            if (instance == null) return null;

            // Handle common DNP3 configuration types that have CurrentValue
            var currentValueProp = type.GetProperty("CurrentValue");
            if (currentValueProp != null)
            {
                try
                {
                    var currentValueType = currentValueProp.PropertyType;
                    var currentValueInstance = Activator.CreateInstance(currentValueType);

                    if (currentValueInstance != null)
                    {
                        // Try to set some default values
                        SetDefaultValues(currentValueInstance, currentValueType);
                        currentValueProp.SetValue(instance, currentValueInstance);
                    }
                }
                catch
                {
                    // If CurrentValue initialization fails, just return the main instance
                    // This will at least make the property non-null
                }
            }

            return instance;
        }
        catch
        {
            return null;
        }
    }

    private void SetDefaultValues(object? instance, Type type)
    {
        if (instance == null) return;

        try
        {
            // Set default values for common properties
            var valueProp = type.GetProperty("Value");
            if (valueProp != null && valueProp.CanWrite && valueProp.PropertyType == typeof(string))
            {
                // Set appropriate defaults based on the type name
                string defaultValue = "Default";

                if (type.Name.Contains("Port") && type.Name.Contains("Name"))
                    defaultValue = "COM1";
                else if (type.Name.Contains("Baud"))
                    defaultValue = "9600";
                else if (type.Name.Contains("Address"))
                    defaultValue = "1";
                else if (type.Name.Contains("Timeout"))
                    defaultValue = "5000";
                else if (type.Name.Contains("Interval"))
                    defaultValue = "1000";

                valueProp.SetValue(instance, defaultValue);
            }

            var addressProp = type.GetProperty("Address");
            if (addressProp != null && addressProp.CanWrite && addressProp.PropertyType == typeof(string))
            {
                addressProp.SetValue(instance, "*************");
            }
        }
        catch
        {
            // Ignore default value setting errors - the property will just be empty
        }
    }

    private static (string SelectedValue, List<string> Options)? GetChoicePropertyInfo(object currentValue)
    {
        try
        {
            var type = currentValue.GetType();
            var properties = type.GetProperties();

            var options = new List<string>();
            string selectedValue = "";

            // Collect all available choice options and find the selected one
            foreach (var prop in properties)
            {
                if (prop.Name == "UserData" || prop.Name.EndsWith("Specified"))
                    continue;

                var displayName = ConvertPropertyNameToDisplay(prop.Name);
                options.Add(displayName);

                // Check if this option is currently selected
                var value = prop.GetValue(currentValue);
                if (value != null)
                {
                    selectedValue = displayName;
                }
            }

            if (options.Count > 1) // Only treat as choice property if there are multiple options
            {
                return (selectedValue, options);
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    private static string GetChoicePropertyDisplayValue(object currentValue)
    {
        var choiceInfo = GetChoicePropertyInfo(currentValue);
        return choiceInfo?.SelectedValue ?? "";
    }

    private static string ConvertPropertyNameToDisplay(string propertyName)
    {
        // Convert camelCase/PascalCase to readable format
        return propertyName switch
        {
            "None" => "None",
            "No" => "No",
            "Yes" => "Yes",
            "NotSupported" => "Not Supported",
            "Rs232Options" => "RS-232",
            "Rs422Options" => "RS-422",
            "Rs485Options" => "RS-485",
            "Asynchronous" => "Asynchronous",
            "Synchronous" => "Synchronous",
            "Other" => "Other",
            _ => propertyName
        };
    }

    private void LoadPropertiesFromObject(object obj)
    {
        var type = obj.GetType();

        // Special handling for DnpDataPointsListType
        if (type.Name == "DnpDataPointsListType")
        {
            LoadDataPointsListProperties(obj);
            return;
        }

        var properties = type.GetProperties();

        foreach (var prop in properties)
        {
            if (prop.CanRead && ShouldDisplayProperty(prop))
            {
                var displayValue = GetDisplayValue(obj, prop);
                if (displayValue != null)
                {
                    Properties.Add(displayValue);
                }
            }
        }
    }

    private void LoadDataPointsListProperties(object dataPointsList)
    {
        var type = dataPointsList.GetType();
        var properties = type.GetProperties();

        // Add summary information
        var typeItem = new PropertyItem
        {
            Name = "Type",
            Type = "Summary",
            IsReadOnly = true
        };
        typeItem.Value = "Data Points Container";
        Properties.Add(typeItem);

        var descItem = new PropertyItem
        {
            Name = "Description",
            Type = "Summary",
            IsReadOnly = true
        };
        descItem.Value = "Contains all data point types for this device";
        Properties.Add(descItem);

        // Count and display each data point type
        foreach (var prop in properties)
        {
            if (ShouldDisplayProperty(prop) && prop.Name.EndsWith("Points"))
            {
                var value = prop.GetValue(dataPointsList);
                string displayValue;

                if (value == null)
                {
                    displayValue = "Not configured";
                }
                else
                {
                    // Try to get count from DataPoints collection
                    var dataPointsProp = value.GetType().GetProperty("DataPoints");
                    if (dataPointsProp != null)
                    {
                        var dataPoints = dataPointsProp.GetValue(value);
                        if (dataPoints is System.Collections.ICollection collection)
                        {
                            displayValue = $"{collection.Count} points";
                        }
                        else
                        {
                            displayValue = "Configured (empty)";
                        }
                    }
                    else
                    {
                        displayValue = "Configured";
                    }
                }

                var dataPointItem = new PropertyItem
                {
                    Name = prop.Name.Replace("Points", " Points"),
                    Type = "DataPointType",
                    IsReadOnly = true
                };
                dataPointItem.Value = displayValue;
                Properties.Add(dataPointItem);
            }
        }
    }

    private static PropertyItem? GetDisplayValue(object obj, System.Reflection.PropertyInfo prop)
    {
        var value = prop.GetValue(obj);

        // Handle simple properties directly
        if (IsSimpleProperty(prop))
        {
            var propertyItem = new PropertyItem
            {
                Name = prop.Name,
                Type = prop.PropertyType.Name,
                IsReadOnly = !prop.CanWrite,
                SourceObject = obj,
                PropertyPath = prop.Name
            };
            propertyItem.Value = value?.ToString() ?? "";
            return propertyItem;
        }

        // Handle null values for complex properties
        if (value == null)
        {
            var propertyItem = new PropertyItem
            {
                Name = prop.Name,
                Type = prop.PropertyType.Name,
                IsReadOnly = true,
                SourceObject = obj,
                PropertyPath = prop.Name
            };
            propertyItem.Value = "(not configured)";
            return propertyItem;
        }

        // Handle DNP3 configuration types with CurrentValue pattern
        var currentValueProp = value.GetType().GetProperty("CurrentValue");
        if (currentValueProp != null)
        {
            var currentValue = currentValueProp.GetValue(value);
            if (currentValue != null)
            {
                // Try "Value" property first
                var valueProp = currentValue.GetType().GetProperty("Value");
                if (valueProp != null)
                {
                    var actualValue = valueProp.GetValue(currentValue);
                    var propertyItem = new PropertyItem
                    {
                        Name = prop.Name,
                        Type = valueProp.PropertyType.Name,
                        IsReadOnly = !valueProp.CanWrite, // Check if the actual Value property is writable
                        SourceObject = obj,
                        PropertyPath = $"{prop.Name}.CurrentValue.Value"
                    };
                    propertyItem.Value = actualValue?.ToString() ?? "";
                    return propertyItem;
                }

                // Try "Address" property (for IP addresses)
                var addressProp = currentValue.GetType().GetProperty("Address");
                if (addressProp != null)
                {
                    var actualValue = addressProp.GetValue(currentValue);
                    var propertyItem = new PropertyItem
                    {
                        Name = prop.Name,
                        Type = addressProp.PropertyType.Name,
                        IsReadOnly = !addressProp.CanWrite,
                        SourceObject = obj,
                        PropertyPath = $"{prop.Name}.CurrentValue.Address"
                    };
                    propertyItem.Value = actualValue?.ToString() ?? "";
                    return propertyItem;
                }

                // Handle choice-based properties (like FlowControl, SupportsCollisionAvoidance)
                var displayValue = GetChoicePropertyDisplayValue(currentValue);
                if (!string.IsNullOrEmpty(displayValue))
                {
                    var propertyItem = new PropertyItem
                    {
                        Name = prop.Name,
                        Type = "Choice",
                        IsReadOnly = true, // Keep choice properties read-only for now
                        SourceObject = obj,
                        PropertyPath = prop.Name
                    };
                    propertyItem.Value = displayValue;
                    return propertyItem;
                }
            }
        }

        // Handle data point collections
        var dataPointsProp = value.GetType().GetProperty("DataPoints");
        if (dataPointsProp != null)
        {
            var dataPoints = dataPointsProp.GetValue(value);
            if (dataPoints is System.Collections.ICollection collection)
            {
                var propertyItem = new PropertyItem
                {
                    Name = prop.Name,
                    Type = "Collection",
                    IsReadOnly = true
                };
                propertyItem.Value = $"{collection.Count} data points";
                return propertyItem;
            }
        }

        return null;
    }

    private static bool ShouldDisplayProperty(System.Reflection.PropertyInfo prop)
    {
        // Skip collections and internal properties
        if (prop.Name == "UserData" || prop.Name.EndsWith("Specified"))
            return false;

        // Include simple properties and DNP3 configuration types
        return IsSimpleProperty(prop) || IsDnp3ConfigProperty(prop);
    }

    private static bool IsSimpleProperty(System.Reflection.PropertyInfo prop)
    {
        var type = prop.PropertyType;

        return type.IsPrimitive ||
               type == typeof(string) ||
               type == typeof(DateTime) ||
               type == typeof(bool) ||
               type.IsEnum ||
               (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>));
    }

    private static bool IsDnp3ConfigProperty(System.Reflection.PropertyInfo prop)
    {
        // Check if this is a DNP3 configuration type that has a CurrentValue property
        var type = prop.PropertyType;
        return type.GetProperty("CurrentValue") != null;
    }
}

public partial class PropertyItem : ObservableObject
{
    public string Name { get; set; } = string.Empty;

    [ObservableProperty]
    private string _value = string.Empty;

    public string Type { get; set; } = string.Empty;
    public bool IsReadOnly { get; set; }
    public object? SourceObject { get; set; }
    public string? PropertyPath { get; set; }



    partial void OnValueChanged(string value)
    {
        // Update the source object when the value changes
        if (SourceObject != null && !string.IsNullOrEmpty(PropertyPath))
        {
            UpdateSourceProperty(value);
        }
    }

    private void UpdateSourceProperty(string newValue)
    {
        if (SourceObject == null || string.IsNullOrEmpty(PropertyPath))
            return;

        try
        {
            var pathParts = PropertyPath.Split('.');
            object current = SourceObject;

            // Navigate to the parent of the final property
            for (int i = 0; i < pathParts.Length - 1; i++)
            {
                var prop = current.GetType().GetProperty(pathParts[i]);
                if (prop == null) return;

                current = prop.GetValue(current);
                if (current == null) return;
            }

            // Set the final property value
            var finalProp = current.GetType().GetProperty(pathParts[^1]);
            if (finalProp != null && finalProp.CanWrite)
            {
                // Convert the string value to the appropriate type
                var convertedValue = ConvertValue(newValue, finalProp.PropertyType);
                finalProp.SetValue(current, convertedValue);
            }
        }
        catch
        {
            // Ignore conversion errors for now
        }
    }



    private static object? ConvertValue(string value, Type targetType)
    {
        if (targetType == typeof(string))
            return value;

        if (targetType == typeof(int) && int.TryParse(value, out var intValue))
            return intValue;

        if (targetType == typeof(double) && double.TryParse(value, out var doubleValue))
            return doubleValue;

        if (targetType == typeof(bool) && bool.TryParse(value, out var boolValue))
            return boolValue;

        // Add more type conversions as needed
        return value;
    }
}
