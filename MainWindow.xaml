<Window x:Class="DNP3Editor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ui="http://schemas.modernwpf.com/2019"
        xmlns:vm="clr-namespace:DNP3Editor.ViewModels"
        xmlns:controls="clr-namespace:DNP3Editor.Controls"
        ui:WindowHelper.UseModernWindowStyle="True"
        Title="{Binding WindowTitle}" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen">



    <Window.InputBindings>
        <KeyBinding Key="N" Modifiers="Ctrl" Command="{Binding NewDocumentCommand}"/>
        <KeyBinding Key="O" Modifiers="Ctrl" Command="{Binding OpenDocumentCommand}"/>
        <KeyBinding Key="S" Modifiers="Ctrl" Command="{Binding SaveDocumentCommand}"/>
        <KeyBinding Key="S" Modifiers="Ctrl+Shift" Command="{Binding SaveAsDocumentCommand}"/>
    </Window.InputBindings>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}">
            <MenuItem Header="_File">
                <MenuItem Header="_New" Command="{Binding NewDocumentCommand}" InputGestureText="Ctrl+N">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Document"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="_Open..." Command="{Binding OpenDocumentCommand}" InputGestureText="Ctrl+O">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="OpenFile"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="_Save" Command="{Binding SaveDocumentCommand}" InputGestureText="Ctrl+S">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="Save"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="Save _As..." Command="{Binding SaveAsDocumentCommand}" InputGestureText="Ctrl+Shift+S"/>
                <Separator/>
                <MenuItem Header="Recent Files" ItemsSource="{Binding RecentFiles}">
                    <MenuItem.ItemTemplate>
                        <DataTemplate>
                            <MenuItem Header="{Binding DisplayName}" Command="{Binding DataContext.OpenRecentCommand, RelativeSource={RelativeSource AncestorType=Window}}" CommandParameter="{Binding FilePath}"/>
                        </DataTemplate>
                    </MenuItem.ItemTemplate>
                </MenuItem>
                <Separator/>
                <MenuItem Header="E_xit" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            
            <MenuItem Header="_Edit">
                <MenuItem Header="_Validate Document" Command="{Binding ValidateDocumentCommand}">
                    <MenuItem.Icon>
                        <ui:SymbolIcon Symbol="CheckMark"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="_Preferences..." Command="{Binding ShowPreferencesCommand}"/>
            </MenuItem>
            
            <MenuItem Header="_View">
                <MenuItem Header="Navigation _Tree" IsCheckable="True" IsChecked="{Binding IsNavigationTreeVisible}"/>
                <MenuItem Header="_Properties Panel" IsCheckable="True" IsChecked="{Binding IsPropertiesPanelVisible}"/>
                <MenuItem Header="_Output Window" IsCheckable="True" IsChecked="{Binding IsOutputWindowVisible}"/>
            </MenuItem>
            
            <MenuItem Header="_Help">
                <MenuItem Header="_Documentation" Command="{Binding ShowDocumentationCommand}"/>
                <MenuItem Header="_About..." Command="{Binding ShowAboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- Toolbar -->
        <ToolBarTray Grid.Row="1" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}">
            <ToolBar>
                <Button Command="{Binding NewDocumentCommand}" ToolTip="New Document (Ctrl+N)">
                    <ui:SymbolIcon Symbol="Document"/>
                </Button>
                <Button Command="{Binding OpenDocumentCommand}" ToolTip="Open Document (Ctrl+O)">
                    <ui:SymbolIcon Symbol="OpenFile"/>
                </Button>
                <Button Command="{Binding SaveDocumentCommand}" ToolTip="Save Document (Ctrl+S)">
                    <ui:SymbolIcon Symbol="Save"/>
                </Button>
                <Separator/>
                <Button Command="{Binding ValidateDocumentCommand}" ToolTip="Validate Document">
                    <ui:SymbolIcon Symbol="CheckMark"/>
                </Button>
                <Separator/>
                <Button Command="{Binding ShowDataPointsCommand}" ToolTip="Manage Data Points">
                    <ui:SymbolIcon Symbol="List"/>
                </Button>
            </ToolBar>
        </ToolBarTray>

        <!-- Main Content Area -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" MinWidth="200"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*" MinWidth="400"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="300" MinWidth="200"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Tree -->
            <GroupBox Grid.Column="0" Header="Document Structure" 
                      Visibility="{Binding IsNavigationTreeVisible, Converter={StaticResource BoolToVisibilityConverter}}">
                <controls:TreeNavigationControl DataContext="{Binding NavigationViewModel}"/>
            </GroupBox>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>

            <!-- Main Editor Area -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="5"/>
                    <RowDefinition Height="150" MinHeight="100"/>
                </Grid.RowDefinitions>

                <!-- Content Editor -->
                <TabControl Grid.Row="0" ItemsSource="{Binding OpenTabs}" SelectedItem="{Binding SelectedTab}">
                    <TabControl.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding Header}" Margin="0,0,5,0"/>
                                <Button Content="×" Command="{Binding DataContext.CloseTabCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                        CommandParameter="{Binding}" Width="16" Height="16" Padding="0" FontSize="10"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabControl.ItemTemplate>
                    <TabControl.ContentTemplate>
                        <DataTemplate>
                            <ContentPresenter Content="{Binding Content}"/>
                        </DataTemplate>
                    </TabControl.ContentTemplate>
                </TabControl>

                <!-- Splitter -->
                <GridSplitter Grid.Row="1" HorizontalAlignment="Stretch" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>

                <!-- Output Window -->
                <GroupBox Grid.Row="2" Header="Output" 
                          Visibility="{Binding IsOutputWindowVisible, Converter={StaticResource BoolToVisibilityConverter}}">
                    <controls:OutputWindowControl DataContext="{Binding OutputViewModel}"/>
                </GroupBox>
            </Grid>

            <!-- Splitter -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}"/>

            <!-- Properties Panel -->
            <GroupBox Grid.Column="4" Header="Properties" 
                      Visibility="{Binding IsPropertiesPanelVisible, Converter={StaticResource BoolToVisibilityConverter}}">
                <controls:PropertiesPanelControl DataContext="{Binding PropertiesViewModel}"/>
            </GroupBox>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Background="{DynamicResource SystemControlBackgroundChromeMediumBrush}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding ValidationStatus}" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding DocumentStatus}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>