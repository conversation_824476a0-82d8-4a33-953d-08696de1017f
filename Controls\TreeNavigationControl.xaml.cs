using System.Windows;
using System.Windows.Controls;
using DNP3Editor.ViewModels;
using DNP3Editor.Models;

namespace DNP3Editor.Controls;

/// <summary>
/// Interaction logic for TreeNavigationControl.xaml
/// </summary>
public partial class TreeNavigationControl : System.Windows.Controls.UserControl
{
    public TreeNavigationControl()
    {
        InitializeComponent();
    }

    private void TreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (DataContext is TreeNavigationViewModel viewModel && e.NewValue is NavigationNode node)
        {
            viewModel.SelectNodeCommand.Execute(node);
        }
    }
}
