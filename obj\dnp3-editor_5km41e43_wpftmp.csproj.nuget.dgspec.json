{"format": 1, "restore": {"C:\\work-repos\\Forge\\dnp3-editor.csproj": {}}, "projects": {"C:\\work-repos\\Forge\\dnp3-editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work-repos\\Forge\\dnp3-editor.csproj", "projectName": "dnp3-editor", "projectPath": "C:\\work-repos\\Forge\\dnp3-editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work-repos\\Forge\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.308/PortableRuntimeIdentifierGraph.json"}}}}}