using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using DNP3Editor.Models;
using DNP3Editor.Events;
using DNP3Editor.Generated;

namespace DNP3Editor.ViewModels;

public partial class TreeNavigationViewModel : ObservableObject
{
    [ObservableProperty]
    private NavigationNode? selectedNode;

    public ObservableCollection<NavigationNode> RootNodes { get; } = [];

    public event EventHandler<NavigationNodeSelectedEventArgs>? NodeSelected;

    [RelayCommand]
    private void SelectNode(NavigationNode node)
    {
        if (SelectedNode != null)
        {
            SelectedNode.IsSelected = false;
        }

        SelectedNode = node;
        node.IsSelected = true;
        
        NodeSelected?.Invoke(this, new NavigationNodeSelectedEventArgs(node));
    }

    [RelayCommand]
    private void ExpandNode(NavigationNode node)
    {
        node.IsExpanded = !node.IsExpanded;
    }

    public void LoadDocument(Dnp3DeviceProfileDocument document)
    {
        RootNodes.Clear();

        var documentNode = new NavigationNode
        {
            Id = "document",
            Name = "DNP3 Device Profile",
            Description = document.DocumentHeader?.DocumentName ?? "Unnamed Document",
            NodeType = NavigationNodeType.Document,
            Data = document,
            IsExpanded = true
        };

        RootNodes.Add(documentNode);

        if (document.ReferenceDevice != null)
        {
            BuildDeviceNodes(documentNode, document.ReferenceDevice);
        }
    }

    private void BuildDeviceNodes(NavigationNode parent, Dnp3DeviceOptionalType device)
    {
        if (device.Configuration != null)
        {
            var configNode = CreateNode("configuration", "Configuration", "Device Configuration", 
                NavigationNodeType.Configuration, device.Configuration, parent);

            BuildConfigurationNodes(configNode, device.Configuration);
        }

        if (device.DataPointsList != null)
        {
            var dataPointsNode = CreateNode("datapoints", "Data Points", "Device Data Points", 
                NavigationNodeType.DataPoints, device.DataPointsList, parent);

            BuildDataPointNodes(dataPointsNode, device.DataPointsList);
        }
    }

    private void BuildConfigurationNodes(NavigationNode parent, DnpConfigurationType configuration)
    {
        if (configuration.DeviceConfig != null)
        {
            CreateNode("deviceinfo", "Device Information", "Basic device information", 
                NavigationNodeType.DeviceInfo, configuration.DeviceConfig, parent);
        }

        if (configuration.SerialConfig != null)
        {
            CreateNode("serial", "Serial Configuration", "Serial communication settings", 
                NavigationNodeType.Serial, configuration.SerialConfig, parent);
        }

        if (configuration.NetworkConfig != null)
        {
            CreateNode("network", "Network Configuration", "Network communication settings",
                NavigationNodeType.Network, configuration.NetworkConfig, parent);
        }

        if (configuration.LinkConfig != null)
        {
            CreateNode("link", "Link Layer Configuration", "Data link layer settings",
                NavigationNodeType.Link, configuration.LinkConfig, parent);
        }

        if (configuration.ApplConfig != null)
        {
            CreateNode("application", "Application Layer Configuration", "Application layer settings",
                NavigationNodeType.Application, configuration.ApplConfig, parent);
        }

        // Conditionally add Master or Outstation configuration based on device function
        var deviceFunction = configuration.DeviceConfig?.DeviceFunction?.CurrentValue;
        if (configuration.MasterConfig != null && deviceFunction?.Master != null)
        {
            CreateNode("master", "Master Configuration", "Master station settings",
                NavigationNodeType.Master, configuration.MasterConfig, parent);
        }

        if (configuration.OutstationConfig != null && deviceFunction?.Outstation != null)
        {
            CreateNode("outstation", "Outstation Configuration", "Outstation settings",
                NavigationNodeType.Outstation, configuration.OutstationConfig, parent);
        }

        if (configuration.SecurityConfig != null)
        {
            CreateNode("security", "Security Configuration", "Security and authentication settings", 
                NavigationNodeType.Security, configuration.SecurityConfig, parent);
        }

        if (configuration.MasterConfig != null)
        {
            CreateNode("master", "Master Configuration", "DNP3 master specific settings", 
                NavigationNodeType.Master, configuration.MasterConfig, parent);
        }

        if (configuration.OutstationConfig != null)
        {
            CreateNode("outstation", "Outstation Configuration", "DNP3 outstation specific settings", 
                NavigationNodeType.Outstation, configuration.OutstationConfig, parent);
        }
    }

    private void BuildDataPointNodes(NavigationNode parent, DnpDataPointsListType dataPoints)
    {
        if (dataPoints.BinaryInputPoints != null)
        {
            var node = CreateNode("binaryinputs", "Binary Inputs",
                $"Binary input points ({dataPoints.BinaryInputPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.BinaryInput, dataPoints.BinaryInputPoints, parent);
        }

        if (dataPoints.DoubleBitInputPoints != null)
        {
            CreateNode("doublebitinputs", "Double-bit Inputs",
                $"Double-bit input points ({dataPoints.DoubleBitInputPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.DoubleBitInput, dataPoints.DoubleBitInputPoints, parent);
        }

        if (dataPoints.BinaryOutputPoints != null)
        {
            CreateNode("binaryoutputs", "Binary Outputs",
                $"Binary output points ({dataPoints.BinaryOutputPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.BinaryOutput, dataPoints.BinaryOutputPoints, parent);
        }

        if (dataPoints.AnalogInputPoints != null)
        {
            CreateNode("analoginputs", "Analog Inputs",
                $"Analog input points ({dataPoints.AnalogInputPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.AnalogInput, dataPoints.AnalogInputPoints, parent);
        }

        if (dataPoints.AnalogOutputPoints != null)
        {
            CreateNode("analogoutputs", "Analog Outputs",
                $"Analog output points ({dataPoints.AnalogOutputPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.AnalogOutput, dataPoints.AnalogOutputPoints, parent);
        }

        if (dataPoints.CounterPoints != null)
        {
            CreateNode("counters", "Counters",
                $"Counter points ({dataPoints.CounterPoints.DataPoints?.Count ?? 0})",
                NavigationNodeType.Counter, dataPoints.CounterPoints, parent);
        }
    }

    private NavigationNode CreateNode(string id, string name, string description, 
        NavigationNodeType nodeType, object data, NavigationNode parent)
    {
        var node = new NavigationNode
        {
            Id = id,
            Name = name,
            Description = description,
            NodeType = nodeType,
            Data = data,
            Parent = parent
        };

        parent.Children.Add(node);
        return node;
    }
}
