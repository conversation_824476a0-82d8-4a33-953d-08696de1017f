<UserControl x:Class="DNP3Editor.Controls.TreeNavigationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             xmlns:models="clr-namespace:DNP3Editor.Models">
    <Grid>
        <TreeView ItemsSource="{Binding RootNodes}" 
                  SelectedItemChanged="TreeView_SelectedItemChanged">
            <TreeView.Resources>
                <HierarchicalDataTemplate DataType="{x:Type models:NavigationNode}" ItemsSource="{Binding Children}">
                    <StackPanel Orientation="Horizontal">
                        <ui:SymbolIcon Symbol="{Binding Icon}"
                                       Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                    </StackPanel>
                </HierarchicalDataTemplate>
            </TreeView.Resources>
        </TreeView>
    </Grid>
</UserControl>
