<UserControl x:Class="DNP3Editor.Controls.OutputWindowControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
            <CheckBox Content="Info" IsChecked="{Binding ShowInfo}" Margin="0,0,10,0"/>
            <CheckBox Content="Warnings" IsChecked="{Binding ShowWarnings}" Margin="0,0,10,0"/>
            <CheckBox Content="Errors" IsChecked="{Binding ShowErrors}" Margin="0,0,10,0"/>
            <Button Content="Clear" Command="{Binding ClearCommand}" Margin="10,0,0,0"/>
        </StackPanel>

        <!-- Messages List -->
        <ListBox Grid.Row="1" ItemsSource="{Binding Messages}" 
                 ScrollViewer.HorizontalScrollBarVisibility="Auto">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <ui:SymbolIcon Grid.Column="0" Margin="0,0,5,0" Width="16" Height="16">
                            <ui:SymbolIcon.Style>
                                <Style TargetType="ui:SymbolIcon">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Type}" Value="Info">
                                            <Setter Property="Symbol" Value="Help"/>
                                            <Setter Property="Foreground" Value="Blue"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Warning">
                                            <Setter Property="Symbol" Value="ReportHacked"/>
                                            <Setter Property="Foreground" Value="Orange"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Error">
                                            <Setter Property="Symbol" Value="Clear"/>
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Type}" Value="Success">
                                            <Setter Property="Symbol" Value="Accept"/>
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ui:SymbolIcon.Style>
                        </ui:SymbolIcon>

                        <TextBlock Grid.Column="1" Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                   Margin="0,0,10,0" VerticalAlignment="Center"
                                   Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
                        
                        <TextBlock Grid.Column="2" Text="{Binding Message}" 
                                   TextWrapping="Wrap" VerticalAlignment="Center"/>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>
