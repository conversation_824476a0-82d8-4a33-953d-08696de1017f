<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.dnp3.org/DNP3/DeviceProfile/November2014"
targetNamespace="http://www.dnp3.org/DNP3/DeviceProfile/November2014" elementFormDefault="qualified" version="2.10.00">
  <!-- Documentation -->
  <xs:annotation>
    <xs:documentation xml:lang="en">

      DNP3 Device Configuration Schema Version 2.10.00

      This document defines an XML representation of the DNP3 Device Profile Document. The top
      level element contains a document header, a single reference device (for an outstation
      or for master station), and one or more auxiliary views of that device. The auxiliary views are always
      optional elements and will typically only include the parameters that are different than the same parameter
      in the reference view.

    </xs:documentation>
  </xs:annotation>
  <!-- ************************************ -->
  <!-- Miscellaneous Data Type Declarations -->
  <!-- ************************************ -->
  <!-- Define an element that has no content -->
  <xs:complexType name="emptyElement"/>
  <!-- Base type for all capabilities -->
  <xs:complexType name="capabilitiesBaseType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Base type for all current values -->
  <xs:complexType name="currentValueBaseType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Capabilities and current value for items that can be enabled or not -->
  <xs:complexType name="yesNoCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="yesNoCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Data type that require an explanation such as 'other' and 'variable' -->
  <xs:complexType name="customType">
    <xs:sequence>
      <xs:element name="explanation" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Data type that require a description such as 'Configurable, other' -->
  <xs:complexType name="configurableCustomType">
    <xs:sequence>
      <xs:element name="description" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Data type that require a description such as 'Other, describe' -->
  <xs:complexType name="notConfigurableCustomType">
    <xs:sequence>
      <xs:element name="description" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Specify The Methods By Which A Device Can Be Configured -->
  <xs:complexType name="softwareType">
    <xs:sequence>
      <xs:element name="name" type="xs:string"/>
      <xs:element name="version" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="configurationMethodsDataType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="xmlViaFileTransfer" type="emptyElement" minOccurs="0"/>
      <xs:element name="xmlViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
      <xs:element name="terminal" type="emptyElement" minOccurs="0"/>
      <xs:element name="proprietaryFileViaFileTransfer" type="emptyElement" minOccurs="0"/>
      <xs:element name="proprietaryFileViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
      <xs:element name="direct" type="emptyElement" minOccurs="0"/>
      <xs:element name="factory" type="emptyElement" minOccurs="0"/>
      <xs:element name="protocol" type="emptyElement" minOccurs="0"/>
      <xs:element name="software" type="softwareType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Event Class Data Type -->
  <xs:simpleType name="eventClassType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="none"/>
      <xs:enumeration value="one"/>
      <xs:enumeration value="two"/>
      <xs:enumeration value="three"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- Included in Class 0 Response Data Type -->
  <xs:simpleType name="includedInClass0ResponseType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="always"/>
      <xs:enumeration value="never"/>
      <xs:enumeration value="onlyWhenAssignedToClass123"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- Control Status Data Type -->
  <xs:simpleType name="controlStatusType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="SUCCESS"/>
      <xs:enumeration value="TIMEOUT"/>
      <xs:enumeration value="NO_SELECT"/>
      <xs:enumeration value="FORMAT_ERROR"/>
      <xs:enumeration value="NOT_SUPPORTED"/>
      <xs:enumeration value="ALREADY_ACTIVE"/>
      <xs:enumeration value="HARDWARE_ERROR"/>
      <xs:enumeration value="LOCAL"/>
      <xs:enumeration value="TOO_MANY_OBJS"/>
      <xs:enumeration value="NOT_AUTHORIZED"/>
      <xs:enumeration value="AUTOMATION_INHIBIT"/>
      <xs:enumeration value="PROCESSING_LIMITED"/>
      <xs:enumeration value="OUT_OF_RANGE"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- Data type that defines a range of integer values -->
  <xs:complexType name="positiveIntRangeType">
    <xs:sequence>
      <xs:element name="minimum" type="xs:positiveInteger"/>
      <xs:element name="maximum" type="xs:positiveInteger"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="nonNegativeIntRangeType">
    <xs:sequence>
      <xs:element name="minimum" type="xs:nonNegativeInteger"/>
      <xs:element name="maximum" type="xs:nonNegativeInteger"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="doubleRangeType">
    <xs:sequence>
      <xs:element name="minimum" type="xs:double"/>
      <xs:element name="maximum" type="xs:double"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Create a type to hold custom user data -->
  <xs:complexType name="userDataType">
    <xs:sequence>
      <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="source" type="xs:anyURI" use="optional"/>
  </xs:complexType>
  <!-- *************** -->
  <!-- Document Header -->
  <!-- *************** -->
  <!-- Define A DNP3 Device Profile Header -->
  <xs:complexType name="documentHeaderType">
    <xs:sequence>
      <xs:element name="documentName" type="xs:string" minOccurs="0"/>
      <xs:element name="documentDescription" type="xs:string" minOccurs="0"/>
      <xs:element name="revisionHistory" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="date" type="xs:date"/>
            <xs:element name="time" type="xs:time" minOccurs="0"/>
            <xs:element name="author" type="xs:string"/>
            <xs:element name="reason" type="xs:string"/>
          </xs:sequence>
          <xs:attribute name="version" type="xs:positiveInteger" use="required"/>
        </xs:complexType>
      </xs:element>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ******************** -->
  <!-- 1) Device Properties -->
  <!-- ******************** -->
  <!-- *********************** -->
  <!-- 1.1) Device Identification -->
  <!-- *********************** -->
  <!-- 1.1.1) Device Function -->
  <xs:complexType name="deviceFunctionCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="master" type="emptyElement" minOccurs="0"/>
          <xs:element name="outstation" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="deviceFunctionCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="master" type="emptyElement" minOccurs="0"/>
          <xs:element name="outstation" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="deviceFunctionType">
    <xs:sequence>
      <xs:element name="capabilities" type="deviceFunctionCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="deviceFunctionCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.2) Vendor Name -->
  <xs:complexType name="vendorNameCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="vendorNameType">
    <xs:sequence>
      <xs:element name="currentValue" type="vendorNameCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.3) Device Name -->
  <xs:complexType name="deviceNameCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="deviceNameType">
    <xs:sequence>
      <xs:element name="currentValue" type="deviceNameCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.4) Hardware Version -->
  <xs:complexType name="hardwareVersionCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="hardwareVersionType">
    <xs:sequence>
      <xs:element name="currentValue" type="hardwareVersionCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.5) Software Version -->
  <xs:complexType name="softwareVersionCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="softwareVersionType">
    <xs:sequence>
      <xs:element name="currentValue" type="softwareVersionCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.6) Device Profile Document Version Number -->
  <xs:complexType name="documentVersionCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:positiveInteger"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="documentVersionNumberType">
    <xs:sequence>
      <xs:element name="currentValue" type="documentVersionCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.7) Highest DNP Level Supported -->
  <xs:complexType name="dnpLevelType">
    <xs:sequence>
      <xs:element name="none" type="emptyElement" minOccurs="0"/>
      <xs:element name="level1" type="emptyElement" minOccurs="0"/>
      <xs:element name="level2" type="emptyElement" minOccurs="0"/>
      <xs:element name="level3" type="emptyElement" minOccurs="0"/>
      <xs:element name="level4" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="dnpLevelCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="master" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requests" type="dnpLevelType" minOccurs="0"/>
                <xs:element name="responses" type="dnpLevelType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="outStation" type="dnpLevelType"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="dnpLevelCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="master" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requests" type="dnpLevelType" minOccurs="0"/>
                <xs:element name="responses" type="dnpLevelType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="outStation" type="dnpLevelType"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="dnpLevelSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="dnpLevelCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="dnpLevelCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.8) Supported Function Blocks -->
  <xs:complexType name="supportedFunctionBlocksCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="selfAddressReservation" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSets" type="emptyElement" minOccurs="0"/>
          <xs:element name="fileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="virtualTerminal" type="emptyElement" minOccurs="0"/>
          <xs:element name="mappingToIEC61850ObjectModels" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC31ActivateConfiguration" type="emptyElement" minOccurs="0"/>
          <xs:element name="secureAuthentication" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportedFunctionBlocksCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="selfAddressReservation" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSets" type="emptyElement" minOccurs="0"/>
          <xs:element name="fileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="virtualTerminal" type="emptyElement" minOccurs="0"/>
          <xs:element name="mappingToIEC61850ObjectModels" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC31ActivateConfiguration" type="emptyElement" minOccurs="0"/>
          <xs:element name="secureAuthentication" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportedFunctionBlocksType">
    <xs:sequence>
      <xs:element name="capabilities" type="supportedFunctionBlocksCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="supportedFunctionBlocksCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.9) Notable Additions -->
  <xs:complexType name="notableAdditionsDataType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="notableAddition" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="notableAdditionsType">
    <xs:sequence>
      <xs:element name="capabilities" type="notableAdditionsDataType" minOccurs="0"/>
      <xs:element name="currentValue" type="notableAdditionsDataType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.10) Methods To Set Configurable Parameters -->
  <xs:complexType name="configurationMethodsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="xmlViaFileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="xmlViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
          <xs:element name="terminal" type="emptyElement" minOccurs="0"/>
          <xs:element name="software" type="softwareType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="proprietaryFileViaFileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="proprietaryFileViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
          <xs:element name="direct" type="emptyElement" minOccurs="0"/>
          <xs:element name="factory" type="emptyElement" minOccurs="0"/>
          <xs:element name="protocol" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="configurationMethodsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="xmlViaFileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="xmlViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
          <xs:element name="terminal" type="emptyElement" minOccurs="0"/>
          <xs:element name="software" type="softwareType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="proprietaryFileViaFileTransfer" type="emptyElement" minOccurs="0"/>
          <xs:element name="proprietaryFileViaOtherTransportMechanism" type="emptyElement" minOccurs="0"/>
          <xs:element name="direct" type="emptyElement" minOccurs="0"/>
          <xs:element name="factory" type="emptyElement" minOccurs="0"/>
          <xs:element name="protocol" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="configurationMethodsType">
    <xs:sequence>
      <xs:element name="capabilities" type="configurationMethodsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="configurationMethodsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.11) DNP3 XML Files Available On-Line -->
  <xs:complexType name="xmlFileType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="readAccess" type="xs:boolean"/>
      <xs:element name="writeAccess" type="xs:boolean"/>
      <xs:element name="filename" type="xs:string"/>
      <xs:element name="description" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="onlineXmlFilesType">
    <xs:sequence>
      <xs:element name="dnpDPReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCapReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCfgReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="xmlFile" type="xmlFileType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="onlineXmlFileNamesType">
    <xs:sequence>
      <xs:element name="capabilities" type="onlineXmlFilesType" minOccurs="0"/>
      <xs:element name="currentValue" type="onlineXmlFilesType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.12) External DNP3 XML Files Available Off-Line -->
  <xs:complexType name="offlineXmlFilesType">
    <xs:sequence>
      <xs:element name="dnpDPReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPWriteSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCapReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCapWriteSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCfgReadSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="dnpDPCfgWriteSupported" type="emptyElement" minOccurs="0"/>
      <xs:element name="xmlFile" type="xmlFileType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="offlineXmlFileNamesType">
    <xs:sequence>
      <xs:element name="capabilities" type="offlineXmlFilesType" minOccurs="0"/>
      <xs:element name="currentValue" type="offlineXmlFilesType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.13) Connections Supported -->
  <xs:complexType name="connectionsSupportedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="serial" type="emptyElement" minOccurs="0"/>
          <xs:element name="network" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="connectionsSupportedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="serial" type="emptyElement" minOccurs="0"/>
          <xs:element name="network" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="connectionsSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="connectionsSupportedCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="connectionsSupportedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.1.14) Conformance Testing -->
  <xs:complexType name="selfTestVersionType">
    <xs:sequence>
      <xs:element name="version" type="xs:string" minOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="indTestVersionType">
    <xs:sequence>
      <xs:element name="version" type="xs:string" minOccurs="1"/>
      <xs:element name="testOrganization" type="xs:string" minOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="conformanceTestingType">
    <xs:sequence>
      <xs:element name="selfTested" type="selfTestVersionType" minOccurs="0"/>
      <xs:element name="independentlyTested" type="indTestVersionType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="deviceConfigType">
    <xs:sequence>
      <!-- 1.1.1) Device Function -->
      <xs:element name="deviceFunction" type="deviceFunctionType" minOccurs="0"/>
      <!-- 1.1.2) Vendor Name -->
      <xs:element name="vendorName" type="vendorNameType" minOccurs="0"/>
      <!-- 1.1.3) Device Name -->
      <xs:element name="deviceName" type="deviceNameType"/>
      <!-- 1.1.4) Hardware Version -->
      <xs:element name="hardwareVersion" type="hardwareVersionType" minOccurs="0"/>
      <!-- 1.1.5) Software Version -->
      <xs:element name="softwareVersion" type="softwareVersionType" minOccurs="0"/>
      <!-- 1.1.6) Device Profile Document Version Number -->
      <xs:element name="documentVersionNumber" type="documentVersionNumberType" minOccurs="0"/>
      <!-- 1.1.7) Highest DNP Level Supported -->
      <xs:element name="dnpLevelSupported" type="dnpLevelSupportedType" minOccurs="0"/>
      <!-- 1.1.8) Supported Function Blocks -->
      <xs:element name="supportedFunctionBlocks" type="supportedFunctionBlocksType" minOccurs="0"/>
      <!-- 1.1.9) Notable Additions -->
      <xs:element name="notableAdditions" type="notableAdditionsType" minOccurs="0"/>
      <!-- 1.1.10) Methods To Set Configurable Parameters -->
      <xs:element name="configurationMethods" type="configurationMethodsType" minOccurs="0"/>
      <!-- 1.1.11) DNP3 XML Files Available On-Line -->
      <xs:element name="onlineXmlFileNames" type="onlineXmlFileNamesType" minOccurs="0"/>
      <!-- 1.1.12) External DNP3 XML Files Available Off-Line -->
      <xs:element name="offlineXmlFileNames" type="offlineXmlFileNamesType" minOccurs="0"/>
      <!-- 1.1.13) Connections Supported -->
      <xs:element name="connectionsSupported" type="connectionsSupportedType" minOccurs="0"/>
      <!-- 1.1.14) Conformance Testing -->
      <xs:element name="conformanceTesting" type="conformanceTestingType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- *********************************** -->
  <!-- 1.2) Serial Connection Capabilities -->
  <!-- *********************************** -->
  <!-- 1.2.1) Port Name -->
  <xs:complexType name="portNameCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="portNameType">
    <xs:sequence>
      <xs:element name="currentValue" type="portNameCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.2) Serial Connection Parameters -->
  <xs:complexType name="serialParametersCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="asynchronous" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="serialParametersCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="asynchronous" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="serialParametersType">
    <xs:sequence>
      <xs:element name="capabilities" type="serialParametersCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="serialParametersCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.3) Baud Rate -->
  <xs:complexType name="baudRateCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="baudRateCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:positiveInteger" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="baudRateType">
    <xs:sequence>
      <xs:element name="capabilities" type="baudRateCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="baudRateCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.4) Flow Control -->
  <xs:complexType name="assertedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="asserted" type="emptyElement" minOccurs="0"/>
          <xs:element name="deasserted" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="assertedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="asserted" type="emptyElement" minOccurs="0"/>
          <xs:element name="deasserted" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="flowControlCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="rs232Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="assertsRTSBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsDTRBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsRTSBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsDTRBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysAssertsRTS" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysAssertsDTR" type="emptyElement" minOccurs="0"/>
                <xs:element name="requiresCTSBeforeTx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresDCDBeforeTx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresDSRBeforeTx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresRIBeforeTx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="rxInactive" type="emptyElement" minOccurs="0"/>
                <xs:element name="requiresCTSBeforeRx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresDCDBeforeRx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresDSRBeforeRx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="requiresRIBeforeRx" type="assertedCapabilitiesType" minOccurs="0"/>
                <xs:element name="alwaysIgnoresCTS" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresDCD" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresDSR" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresRI" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="rs422Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requiresIndicationBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsControlBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="rs485Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requiresRxInactiveBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="flowControlCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="rs232Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="assertsRTSBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsDTRBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsRTSBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsDTRBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysAssertsRTS" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysAssertsDTR" type="emptyElement" minOccurs="0"/>
                <xs:element name="requiresCTSBeforeTx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresDCDBeforeTx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresDSRBeforeTx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresRIBeforeTx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="rxInactive" type="emptyElement" minOccurs="0"/>
                <xs:element name="requiresCTSBeforeRx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresDCDBeforeRx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresDSRBeforeRx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="requiresRIBeforeRx" type="assertedCurrentValueType" minOccurs="0"/>
                <xs:element name="alwaysIgnoresCTS" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresDCD" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresDSR" type="emptyElement" minOccurs="0"/>
                <xs:element name="alwaysIgnoresRI" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="rs422Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requiresIndicationBeforeRx" type="emptyElement" minOccurs="0"/>
                <xs:element name="assertsControlBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="rs485Options" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="requiresRxInactiveBeforeTx" type="emptyElement" minOccurs="0"/>
                <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="flowControlType">
    <xs:sequence>
      <xs:element name="capabilities" type="flowControlCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="flowControlCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.5) Link Status Interval -->
  <xs:complexType name="linkStatusIntervalCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="linkStatusIntervalCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="linkStatusIntervalType">
    <xs:sequence>
      <xs:element name="capabilities" type="linkStatusIntervalCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="linkStatusIntervalCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.6) Supports DNP3 Collision Avoidance -->
  <xs:complexType name="backoffTimeType">
    <xs:sequence>
      <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
      <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
      <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="supportsCollisionAvoidanceCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="minimumBackoffTime" type="backoffTimeType"/>
                <xs:element name="maximumRandomBackoffTime" type="backoffTimeType"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsCollisionAvoidanceCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="minimumBackoffTime" type="xs:nonNegativeInteger"/>
                <xs:element name="maximumRandomBackoffTime" type="xs:nonNegativeInteger"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsCollisionAvoidanceType">
    <xs:sequence>
      <xs:element name="capabilities" type="supportsCollisionAvoidanceCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="supportsCollisionAvoidanceCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.7) Receiver Inter-character Timeout -->
  <xs:complexType name="interCharacterTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notChecked" type="emptyElement" minOccurs="0"/>
          <xs:element name="noGapPermitted" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedBitTimes" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="rangeBitTimes" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectableBitTimes" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="fixedMilliseconds" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="rangeMilliseconds" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectableMilliseconds" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="interCharacterTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notChecked" type="emptyElement" minOccurs="0"/>
          <xs:element name="noGapPermitted" type="emptyElement" minOccurs="0"/>
          <xs:element name="valueBitTimes" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="valueMilliseconds" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="interCharacterTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="interCharacterTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="interCharacterTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.2.8) Inter-character gaps in transmission -->
  <xs:complexType name="interCharacterGapCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="maximumBitTimes" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="maximumMilliseconds" type="xs:positiveInteger" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="interCharacterGapCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="maximumBitTimes" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="maximumMilliseconds" type="xs:positiveInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="interCharacterGapType">
    <xs:sequence>
      <xs:element name="capabilities" type="interCharacterGapCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="interCharacterGapCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="serialConfigType">
    <xs:sequence>
      <!-- 1.2.1) Port Name -->
      <xs:element name="portName" type="portNameType" minOccurs="0"/>
      <!-- 1.2.2) Serial Connection Parameters -->
      <xs:element name="serialParameters" type="serialParametersType" minOccurs="0"/>
      <!-- 1.2.3) Baud Rate -->
      <xs:element name="baudRate" type="baudRateType" minOccurs="0"/>
      <!-- 1.2.4) Flow Control -->
      <xs:element name="flowControl" type="flowControlType" minOccurs="0"/>
      <!-- 1.2.5) Link Status Interval -->
      <xs:element name="linkStatusInterval" type="linkStatusIntervalType" minOccurs="0"/>
      <!-- 1.2.6) Supports DNP3 Collision Avoidance -->
      <xs:element name="supportsCollisionAvoidance" type="supportsCollisionAvoidanceType" minOccurs="0"/>
      <!-- 1.2.7) Receiver Inter-character Timeout -->
      <xs:element name="interCharacterTimeout" type="interCharacterTimeoutType" minOccurs="0"/>
      <!-- 1.2.8) Inter-character gaps in transmission -->
      <xs:element name="interCharacterGap" type="interCharacterGapType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- *********************************** -->
  <!-- 1.3) IP Networking Capabilities -->
  <!-- *********************************** -->
  <!-- 1.3.1) Port Name -->
  <xs:complexType name="ipPortNameCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="value" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ipPortNameType">
    <xs:sequence>
      <xs:element name="currentValue" type="ipPortNameCurrentValueType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.2) Type of End Point -->
  <xs:complexType name="typeOfEndPointCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="tcpInitiating" type="emptyElement" minOccurs="0"/>
          <xs:element name="tcpListening" type="emptyElement" minOccurs="0"/>
          <xs:element name="tcpDual" type="emptyElement" minOccurs="0"/>
          <xs:element name="udpDatagram" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="typeOfEndPointCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="tcpInitiating" type="emptyElement" minOccurs="0"/>
          <xs:element name="tcpListening" type="emptyElement" minOccurs="0"/>
          <xs:element name="tcpDual" type="emptyElement" minOccurs="0"/>
          <xs:element name="udpDatagram" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="typeOfEndPointType">
    <xs:sequence>
      <xs:element name="capabilities" type="typeOfEndPointCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="typeOfEndPointCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.3) IP Address of this Device -->
  <xs:complexType name="ipAddressCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="address" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ipAddressType">
    <xs:sequence>
      <xs:element name="currentValue" type="ipAddressCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.4) Subnet Mask -->
  <xs:complexType name="subnetMaskCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="mask" type="xs:string"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="subnetMaskType">
    <xs:sequence>
      <xs:element name="currentValue" type="subnetMaskCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.5) Gateway IP Address -->
  <xs:complexType name="gatewayIPAddressType">
    <xs:sequence>
      <xs:element name="currentValue" type="ipAddressCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.6) TCP Connection Establishment -->
  <xs:complexType name="tcpConnectionEstablishmentCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="allowsAll" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnIPAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnListOfIPAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnWildcardIPAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnListOfWildcardIPAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpConnectionEstablishmentCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="allowsAll" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnIPAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnListOfIPAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnWildcardIPAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnListOfWildcardIPAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpConnectionEstablishmentType">
    <xs:sequence>
      <xs:element name="capabilities" type="tcpConnectionEstablishmentCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="tcpConnectionEstablishmentCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.7) IP Address Of Remote Device-->
  <xs:complexType name="ipAddressesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="address" type="xs:string" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ipAddressOfRemoteDeviceType">
    <xs:sequence>
      <xs:element name="currentValue" type="ipAddressesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.8) TCP Listen Port Number -->
  <xs:complexType name="tcpListenPortCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpListenPortCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpListenPortType">
    <xs:sequence>
      <xs:element name="capabilities" type="tcpListenPortCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="tcpListenPortCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.9) TCP Listen Port of Remote Device -->
  <xs:complexType name="tcpPortOfRemoteDeviceCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpPortOfRemoteDeviceCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpPortOfRemoteDeviceType">
    <xs:sequence>
      <xs:element name="capabilities" type="tcpPortOfRemoteDeviceCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="tcpPortOfRemoteDeviceCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.10) TCP Keep-alive Timer -->
  <xs:complexType name="tcpKeepAliveTimerCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpKeepAliveTimerCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="tcpKeepAliveTimerType">
    <xs:sequence>
      <xs:element name="capabilities" type="tcpKeepAliveTimerCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="tcpKeepAliveTimerCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.11) Local UDP port -->
  <xs:complexType name="localUDPPortCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="letSystemChoose" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="localUDPPortCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="letSystemChoose" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="localUDPPortType">
    <xs:sequence>
      <xs:element name="capabilities" type="localUDPPortCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="localUDPPortCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.12) Destination UDP port for DNP3 Requests -->
  <xs:complexType name="destinationUDPPortCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="destinationUDPPortCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="destinationUDPPortType">
    <xs:sequence>
      <xs:element name="capabilities" type="destinationUDPPortCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="destinationUDPPortCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.13) Destination UDP port for unsolicited null responses -->
  <xs:complexType name="udpPortForUnsolicitedNullResponsesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="udpPortForUnsolicitedNullResponsesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="udpPortForUnsolicitedNullResponsesType">
    <xs:sequence>
      <xs:element name="capabilities" type="udpPortForUnsolicitedNullResponsesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="udpPortForUnsolicitedNullResponsesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.14) Destination UDP port for responses -->
  <xs:complexType name="udpPortForResponsesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedAt20000" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="useSourcePortNumber" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="udpPortForResponsesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="useSourcePortNumber" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="udpPortForResponsesType">
    <xs:sequence>
      <xs:element name="capabilities" type="udpPortForResponsesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="udpPortForResponsesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.15) Multiple Outstation Connections -->
  <xs:complexType name="multipleOutstationConnectionsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="supportsMultipleOutstations" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="multipleOutstationConnectionsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="supportsMultipleOutstations" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="multipleOutstationConnectionsType">
    <xs:sequence>
      <xs:element name="capabilities" type="multipleOutstationConnectionsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="multipleOutstationConnectionsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.16) Multiple Master Connections -->
  <xs:complexType name="multipleMasterConnectionsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnIPAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnIPPortNumber" type="emptyElement" minOccurs="0"/>
          <xs:element name="browsingForStaticData" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="multipleMasterConnectionsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:choice>
            <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
            <xs:sequence>
              <xs:element name="basedOnIPAddress" type="emptyElement" minOccurs="0"/>
              <xs:element name="basedOnIPPortNumber" type="emptyElement" minOccurs="0"/>
              <xs:element name="browsingForStaticData" type="emptyElement" minOccurs="0"/>
            </xs:sequence>
          </xs:choice>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="multipleMasterConnectionsType">
    <xs:sequence>
      <xs:element name="capabilities" type="multipleMasterConnectionsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="multipleMasterConnectionsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.3.17) Time Synchronization Support -->
  <xs:complexType name="timeSynchronizationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="dnpLANProcedure" type="emptyElement" minOccurs="0"/>
          <xs:element name="dnpWriteTimeProcedure" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timeSynchronizationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="dnpLANProcedure" type="emptyElement" minOccurs="0"/>
          <xs:element name="dnpWriteTimeProcedure" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timeSynchronizationType">
    <xs:sequence>
      <xs:element name="capabilities" type="timeSynchronizationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="timeSynchronizationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="networkConfigType">
    <xs:sequence>
      <!-- 1.3.1) Port Name -->
      <xs:element name="portName" type="ipPortNameType" minOccurs="0"/>
      <!-- 1.3.2) Type of End Point -->
      <xs:element name="typeOfEndPoint" type="typeOfEndPointType" minOccurs="0"/>
      <!-- 1.3.3) IP Address of this Device -->
      <xs:element name="ipAddress" type="ipAddressType" minOccurs="0"/>
      <!-- 1.3.4) Subnet Mask -->
      <xs:element name="subnetMask" type="subnetMaskType" minOccurs="0"/>
      <!-- 1.3.5) Gateway IP Address -->
      <xs:element name="gatewayIPAddress" type="gatewayIPAddressType" minOccurs="0"/>
      <!-- 1.3.6) TCP Connection Establishment -->
      <xs:element name="tcpConnectionEstablishment" type="tcpConnectionEstablishmentType" minOccurs="0"/>
      <!-- 1.3.7) IP Address Of Remote Device-->
      <xs:element name="ipAddressOfRemoteDevice" type="ipAddressOfRemoteDeviceType" minOccurs="0"/>
      <!-- 1.3.8) TCP Listen Port -->
      <xs:element name="tcpListenPort" type="tcpListenPortType" minOccurs="0"/>
      <!-- 1.3.9) TCP Listen Port of Remote Device -->
      <xs:element name="tcpPortOfRemoteDevice" type="tcpPortOfRemoteDeviceType" minOccurs="0"/>
      <!-- 1.3.10) TCP Keep-alive Timer -->
      <xs:element name="tcpKeepAliveTimer" type="tcpKeepAliveTimerType" minOccurs="0"/>
      <!-- 1.3.11) Local UDP port -->
      <xs:element name="localUDPPort" type="localUDPPortType" minOccurs="0"/>
      <!-- 1.3.12) Destination UDP port for DNP3 Requests -->
      <xs:element name="destinationUDPPort" type="destinationUDPPortType" minOccurs="0"/>
      <!-- 1.3.13) Destination UDP port for unsolicited null responses -->
      <xs:element name="udpPortForUnsolicitedNullResponses" type="udpPortForUnsolicitedNullResponsesType" minOccurs="0"/>
      <!-- 1.3.14) Destination UDP port for responses -->
      <xs:element name="udpPortForResponses" type="udpPortForResponsesType" minOccurs="0"/>
      <!-- 1.3.15) Multiple Outstation Connections -->
      <xs:element name="multipleOutstationConnections" type="multipleOutstationConnectionsType" minOccurs="0"/>
      <!-- 1.3.16) Multiple Master Connections -->
      <xs:element name="multipleMasterConnections" type="multipleMasterConnectionsType" minOccurs="0"/>
      <!-- 1.3.17) Time Synchronization Support -->
      <xs:element name="timeSynchronization" type="timeSynchronizationType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ***************************** -->
  <!-- 1.4) Link Layer Capabilities -->
  <!-- ***************************** -->
  <!-- 1.4.1) Data Link Address -->
  <xs:simpleType name="dataLinkAddressDataType">
    <xs:restriction base="xs:unsignedShort">
      <xs:maxExclusive value="65520"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="dataLinkAddressRangeType">
    <xs:sequence>
      <xs:element name="minimum" type="dataLinkAddressDataType"/>
      <xs:element name="maximum" type="dataLinkAddressDataType"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="dataLinkAddressCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="dataLinkAddressDataType" minOccurs="0"/>
          <xs:element name="range" type="dataLinkAddressRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="dataLinkAddressDataType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="dataLinkAddressCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="dataLinkAddressDataType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="dataLinkAddressType">
    <xs:sequence>
      <xs:element name="capabilities" type="dataLinkAddressCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="dataLinkAddressCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.2) DNP3 Source Address Validation -->
  <xs:complexType name="sourceAddressValidationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="alwaysSingleAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="alwaysMultipleAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sourceAddressValidationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="alwaysSingleAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="alwaysMultipleAddresses" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sourceAddressValidationType">
    <xs:sequence>
      <xs:element name="capabilities" type="sourceAddressValidationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sourceAddressValidationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.3) DNP3 Source Address Expected When Validation is Enabled -->
  <xs:complexType name="expectedSourceAddressCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="anyDataLinkAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="dataLinkAddressRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="dataLinkAddressDataType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="expectedSourceAddressCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="anyDataLinkAddress" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="dataLinkAddressDataType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="expectedSourceAddressType">
    <xs:sequence>
      <xs:element name="capabilities" type="expectedSourceAddressCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="expectedSourceAddressCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.4) Self Address Support Using Address 0xFFFC -->
  <xs:complexType name="selfAddressSupportCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="selfAddressSupportCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="selfAddressSupportType">
    <xs:sequence>
      <xs:element name="capabilities" type="selfAddressSupportCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="selfAddressSupportCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.5) Sends Confirmed User Data Frames -->
  <xs:complexType name="sendsConfirmedUserDataFramesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sendsConfirmedUserDataFramesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sendsConfirmedUserDataFramesType">
    <xs:sequence>
      <xs:element name="capabilities" type="sendsConfirmedUserDataFramesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sendsConfirmedUserDataFramesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.6) Data Link Layer Confirmation Timeout -->
  <xs:complexType name="linkLayerConfirmTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="linkLayerConfirmTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="linkLayerConfirmTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="linkLayerConfirmTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="linkLayerConfirmTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.7) Maximum Data Link Retries -->
  <xs:complexType name="maxDataLinkRetriesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxDataLinkRetriesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxDataLinkRetriesType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxDataLinkRetriesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxDataLinkRetriesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.8) Max Number of Octets Transmitted in a Data Link Frame -->
  <xs:complexType name="maxFrameSizeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxFrameSizeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:positiveInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTransmittedFrameSizeType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxFrameSizeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxFrameSizeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.4.9) Max Number of Octets that can be Received in a Data Link Frame -->
  <xs:complexType name="maxReceivedFrameSizeType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxFrameSizeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxFrameSizeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="linkConfigType">
    <xs:sequence>
      <!-- 1.4.1) Data Link Address -->
      <xs:element name="dataLinkAddress" type="dataLinkAddressType" minOccurs="0"/>
      <!-- 1.4.2) DNP3 Source Address Validation -->
      <xs:element name="sourceAddressValidation" type="sourceAddressValidationType" minOccurs="0"/>
      <!-- 1.4.3) DNP3 Source Address Expected When Validation is Enabled -->
      <xs:element name="expectedSourceAddress" type="expectedSourceAddressType" minOccurs="0"/>
      <!-- 1.4.4) Self Address Support Using Address 0xFFFC -->
      <xs:element name="selfAddressSupport" type="selfAddressSupportType" minOccurs="0"/>
      <!-- 1.4.5) Sends Confirmed User Data Frames -->
      <xs:element name="sendsConfirmedUserDataFrames" type="sendsConfirmedUserDataFramesType" minOccurs="0"/>
      <!-- 1.4.6) Data Link Layer Confirmation Timeout -->
      <xs:element name="linkLayerConfirmTimeout" type="linkLayerConfirmTimeoutType" minOccurs="0"/>
      <!-- 1.4.7) Maximum Data Link Retries -->
      <xs:element name="maxDataLinkRetries" type="maxDataLinkRetriesType" minOccurs="0"/>
      <!-- 1.4.8) Max Number of Octets Transmitted in a Data Link Frame -->
      <xs:element name="maxTransmittedFrameSize" type="maxTransmittedFrameSizeType" minOccurs="0"/>
      <!-- 1.4.9) Max Number of Octets that can be Received in a Data Link Frame -->
      <xs:element name="maxReceivedFrameSize" type="maxReceivedFrameSizeType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ************************************ -->
  <!-- 1.5) Application Layer Capabilities  -->
  <!-- ************************************ -->
  <!-- 1.5.1) Maximum number of octets Transmitted In An Application Layer Fragment other than File Transfer -->
  <xs:complexType name="maxFragmentSizeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxFragmentSizeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:positiveInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTransmittedFragmentSizeType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxFragmentSizeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxFragmentSizeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.2) Maximum number of octets Transmitted in an Application Layer Fragment containing File Transfer -->
  <xs:complexType name="maxFileTransferTransmittedFragmentSizeType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxFragmentSizeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxFragmentSizeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.3) Maximum Number of Octets that can be Received in an Application Layer Fragment -->
  <xs:complexType name="maxReceivedFragmentSizeType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxFragmentSizeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxFragmentSizeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.4) Timeout Waiting for Complete Application Layer Fragment (ms) -->
  <xs:complexType name="fragmentTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="fragmentTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="fragmentTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="fragmentTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="fragmentTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.5) Maximum Number of Objects Allowed in a Single Control Request for CROB -->
  <xs:complexType name="maxObjectsControlRequestCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxObjectsControlRequestCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxObjectsInCROBControlRequestType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxObjectsControlRequestCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxObjectsControlRequestCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.6) Maximum Number of Objects Allowed in a Single Control Request for Analog Outputs -->
  <xs:complexType name="maxObjectsInAnalogOutputControlRequestType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxObjectsControlRequestCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxObjectsControlRequestCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.7) Maximum Number of Objects Allowed in a Single Control Request for Data Sets -->
  <xs:complexType name="maxObjectsInDataSetsControlRequestType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxObjectsControlRequestCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxObjectsControlRequestCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.8) Supports Mixing Object Groups (AOBs, CROBs, and Data Sets) in the Same Control Request-->
  <xs:complexType name="supportsMixedObjectGroupsInControlCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsMixedObjectGroupsInControlCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsMixedObjectGroupsInControlRequestType">
    <xs:sequence>
      <xs:element name="capabilities" type="supportsMixedObjectGroupsInControlCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="supportsMixedObjectGroupsInControlCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.5.9) Control Status Codes Supported: -->
  <xs:complexType name="controlStatusCodesSupportedCapabilitiesType">
    <xs:sequence>
      <xs:element name="code1" type="emptyElement" minOccurs="0"/>
      <xs:element name="code2" type="emptyElement" minOccurs="0"/>
      <xs:element name="code3" type="emptyElement" minOccurs="0"/>
      <xs:element name="code4" type="emptyElement" minOccurs="0"/>
      <xs:element name="code5" type="emptyElement" minOccurs="0"/>
      <xs:element name="code6" type="emptyElement" minOccurs="0"/>
      <xs:element name="code7" type="emptyElement" minOccurs="0"/>
      <xs:element name="code8" type="emptyElement" minOccurs="0"/>
      <xs:element name="code9" type="emptyElement" minOccurs="0"/>
      <xs:element name="code10" type="emptyElement" minOccurs="0"/>
      <xs:element name="code11" type="emptyElement" minOccurs="0"/>
      <xs:element name="code12" type="emptyElement" minOccurs="0"/>
      <xs:element name="code13" type="emptyElement" minOccurs="0"/>
      <xs:element name="code14" type="emptyElement" minOccurs="0"/>
      <xs:element name="code15" type="emptyElement" minOccurs="0"/>
      <xs:element name="code16" type="emptyElement" minOccurs="0"/>
      <xs:element name="code17" type="emptyElement" minOccurs="0"/>
      <xs:element name="code18" type="emptyElement" minOccurs="0"/>
      <xs:element name="code126" type="emptyElement" minOccurs="0"/>
      <xs:element name="code127" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="controlStatusCodesSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="controlStatusCodesSupportedCapabilitiesType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Application Layer Configuration Parameters -->
  <xs:complexType name="applConfigType">
    <xs:sequence>
      <!-- 1.5.1) Maximum number of octets Transmitted In An Application Layer Fragment other than File Transfer -->
      <xs:element name="maxTransmittedFragmentSize" type="maxTransmittedFragmentSizeType" minOccurs="0"/>
      <!-- 1.5.1) Maximum number of octets Transmitted In An Application Layer Fragment containing File Transfer -->
      <xs:element name="maxFileTransferTransmittedFragmentSize" type="maxFileTransferTransmittedFragmentSizeType" minOccurs="0"/>
      <!-- 1.5.3) Maximum Number of Octets that can be Received in an Application Layer Fragment -->
      <xs:element name="maxReceivedFragmentSize" type="maxReceivedFragmentSizeType" minOccurs="0"/>
      <!-- 1.5.4) Timeout Waiting for Complete Application Layer Fragment (ms) -->
      <xs:element name="fragmentTimeout" type="fragmentTimeoutType" minOccurs="0"/>
      <!-- 1.5.5) Maximum Number of Objects Allowed in a Single Control Request for CROB -->
      <xs:element name="maxObjectsInCROBControlRequest" type="maxObjectsInCROBControlRequestType" minOccurs="0"/>
      <!-- 1.5.6) Maximum Number of Objects Allowed in a Single Control Request for Analog Outputs -->
      <xs:element name="maxObjectsInAnalogOutputControlRequest" type="maxObjectsInAnalogOutputControlRequestType" minOccurs="0"/>
      <!-- 1.5.7) Maximum Number of Objects Allowed in a Single Control Request for Data Sets -->
      <xs:element name="maxObjectsInDataSetsControlRequest" type="maxObjectsInDataSetsControlRequestType" minOccurs="0"/>
      <!-- 1.5.8) Supports Mixing Object Groups (AOBs, CROBs, and Data Sets) in the Same Control Request-->
      <xs:element name="supportsMixedObjectGroupsInControlRequest" type="supportsMixedObjectGroupsInControlRequestType" minOccurs="0"/>
      <!-- 1.5.9) Control Status Codes Supported-->
      <xs:element name="controlStatusCodesSupported" type="controlStatusCodesSupportedType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ************************ -->
  <!-- 1.6) Master Capabilities -->
  <!-- ************************ -->
  <!-- 1.6.1) Timeout Waiting for Complete Application Layer Response -->
  <xs:complexType name="responseTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="responseTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="responseTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="responseTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="responseTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.2) Maximum Application Layer Retries for Request Messages -->
  <xs:complexType name="applicationLayerRetriesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="applicationLayerRetriesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="applicationLayerRetriesType">
    <xs:sequence>
      <xs:element name="capabilities" type="applicationLayerRetriesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="applicationLayerRetriesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.3) Incremental Timeout Waiting for First or Next Fragment of an Application Layer Response -->
  <xs:complexType name="responseIncrementalTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="responseIncrementalTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="responseIncrementalTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="responseIncrementalTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="responseIncrementalTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.4) Issues controls to off-line devices-->
  <xs:complexType name="issuesControlsToOfflineDevicesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="issuesControlsToOfflineDevicesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="issuesControlsToOfflineDevicesType">
    <xs:sequence>
      <xs:element name="capabilities" type="issuesControlsToOfflineDevicesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="issuesControlsToOfflineDevicesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.5) Issues controls to off-scan devices-->
  <xs:complexType name="issuesControlsToOffscanDevicesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="issuesControlsToOffscanDevicesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="issuesControlsToOffscanDevicesType">
    <xs:sequence>
      <xs:element name="capabilities" type="issuesControlsToOffscanDevicesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="issuesControlsToOffscanDevicesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.6) Max control retries same sequence number -->
  <xs:complexType name="maxControlRetriesSameSNCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxControlRetriesSameSNCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxControlRetriesSameSNType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxControlRetriesSameSNCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxControlRetriesSameSNCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.7) Max control retries new sequence number -->
  <xs:complexType name="maxControlRetriesNewSNCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxControlRetriesNewSNCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxControlRetriesNewSNType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxControlRetriesNewSNCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxControlRetriesNewSNCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.8) Maximum error in the time that the Master issues scheduled freeze requests: -->
  <xs:complexType name="maxTimeErrorScheduledFreezesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="time" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTimeErrorScheduledFreezesType">
    <xs:sequence>
      <xs:element name="currentValue" type="maxTimeErrorScheduledFreezesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.9) Maximum error in the time that the Master issues repetitive freeze requests: -->
  <xs:complexType name="maxTimeErrorRepetitiveFreezesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="time" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTimeErrorRepetitiveFreezesType">
    <xs:sequence>
      <xs:element name="currentValue" type="maxTimeErrorRepetitiveFreezesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.10) Scheduled actions that may affect the accuracy of freeze requests: -->
  <xs:complexType name="actionsAffectingFreezeRequestsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="pollRequests" type="emptyElement" minOccurs="0"/>
          <xs:element name="controlRequests" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="actionsAffectingFreezeRequestsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="pollRequests" type="emptyElement" minOccurs="0"/>
          <xs:element name="controlRequests" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="actionsAffectingFreezeRequestsType">
    <xs:sequence>
      <xs:element name="capabilities" type="actionsAffectingFreezeRequestsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="actionsAffectingFreezeRequestsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.6.11) Scheduling algorithm -->
  <xs:complexType name="schedulingAlgorithmDataType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="schedulingAlgorithm" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="schedulingAlgorithmType">
    <xs:sequence>
      <xs:element name="capabilities" type="schedulingAlgorithmDataType" minOccurs="0"/>
      <xs:element name="currentValue" type="schedulingAlgorithmDataType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="masterConfigType">
    <xs:sequence>
      <!-- 1.6.1) Timeout Waiting for Complete Application Layer Response -->
      <xs:element name="responseTimeout" type="responseTimeoutType" minOccurs="0"/>
      <!-- 1.6.2) Maximum Application Layer Retries for Request Messages -->
      <xs:element name="applicationLayerRetries" type="applicationLayerRetriesType" minOccurs="0"/>
      <!-- 1.6.3) Incremental Timeout Waiting for First or Next Fragment of an Application Layer Response -->
      <xs:element name="responseIncrementalTimeout" type="responseIncrementalTimeoutType" minOccurs="0"/>
      <!-- 1.6.4) Issuing controls to off-line devices -->
      <xs:element name="issuesControlsToOfflineDevices" type="issuesControlsToOfflineDevicesType" minOccurs="0"/>
      <!-- 1.6.5) Issuing controls to off-scan devices -->
      <xs:element name="issuesControlsToOffscanDevices" type="issuesControlsToOffscanDevicesType" minOccurs="0"/>
      <!-- 1.6.6) Max control retries same sequence number -->
      <xs:element name="maxControlRetriesSameSN" type="maxControlRetriesSameSNType" minOccurs="0"/>
      <!-- 1.6.7) Max control retries new sequence number -->
      <xs:element name="maxControlRetriesNewSN" type="maxControlRetriesNewSNType" minOccurs="0"/>
      <!-- 1.6.8) Maximum error in the time that the Master issues freeze requests -->
      <xs:element name="maxTimeErrorScheduledFreezes" type="maxTimeErrorScheduledFreezesType" minOccurs="0"/>
      <!-- 1.6.9) Maximum error in the time that the Master issues freeze requests -->
      <xs:element name="maxTimeErrorRepetitiveFreezes" type="maxTimeErrorRepetitiveFreezesType" minOccurs="0"/>
      <!-- 1.6.10) Scheduled actions that may affect the accuracy of freeze requests: -->
      <xs:element name="actionsAffectingFreezeRequests" type="actionsAffectingFreezeRequestsType" minOccurs="0"/>
      <!-- 1.6.11) Scheduling algorithm -->
      <xs:element name="schedulingAlgorithm" type="schedulingAlgorithmType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- **************************** -->
  <!-- 1.7) Outstation Capabilities -->
  <!-- **************************** -->
  <!-- 1.7.1) Timeout Waiting for Application Confirm of Solicited Response Message (ms) -->
  <xs:complexType name="applicationLayerConfirmTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="applicationLayerConfirmTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="applicationLayerConfirmTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="applicationLayerConfirmTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="applicationLayerConfirmTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.2) How often is Time Synchronization Required from the Master -->
  <xs:complexType name="timeSyncRequiredCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="withinSecondsOfIIN14" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="periodicallyFixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="periodicallyRange" type="positiveIntRangeType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timeSyncRequiredCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="withinSecondsOfIIN14" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="periodically" type="xs:positiveInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timeSyncRequiredType">
    <xs:sequence>
      <xs:element name="capabilities" type="timeSyncRequiredCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="timeSyncRequiredCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.3) Device Trouble Bit IIN 1.6 -->
  <xs:complexType name="deviceTroubleBitCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="neverUsed" type="emptyElement" minOccurs="0"/>
          <xs:element name="reasonForSetting" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="deviceTroubleBitCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="neverUsed" type="emptyElement" minOccurs="0"/>
          <xs:element name="reasonForSetting" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="deviceTroubleBitType">
    <xs:sequence>
      <xs:element name="capabilities" type="deviceTroubleBitCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="deviceTroubleBitCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.4) File Handle Timeout -->
  <xs:complexType name="fileHandleTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="fileHandleTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="fileHandleTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="fileHandleTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="fileHandleTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.5) Event Buffer Overflow Behavior -->
  <xs:complexType name="eventBufferOverflowBehaviorCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="discardOldest" type="emptyElement" minOccurs="0"/>
          <xs:element name="discardNewest" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventBufferOverflowBehaviorCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="discardOldest" type="emptyElement" minOccurs="0"/>
          <xs:element name="discardNewest" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventBufferOverflowBehaviorType">
    <xs:sequence>
      <xs:element name="capabilities" type="eventBufferOverflowBehaviorCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="eventBufferOverflowBehaviorCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.6) Event Buffer Organization -->
  <xs:complexType name="numberOfEventsType">
    <xs:sequence>
      <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
      <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
      <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="perClassType">
    <xs:sequence>
      <xs:element name="class1" type="numberOfEventsType" minOccurs="0"/>
      <xs:element name="class2" type="numberOfEventsType" minOccurs="0"/>
      <xs:element name="class3" type="numberOfEventsType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="eventBufferOrganizationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="perObjectGroup" type="emptyElement" minOccurs="0"/>
          <xs:element name="perClass" type="perClassType" minOccurs="0"/>
          <xs:element name="singleBuffer" type="numberOfEventsType" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="classValueType">
    <xs:sequence>
      <xs:element name="class1Value" type="xs:nonNegativeInteger" minOccurs="0"/>
      <xs:element name="class2Value" type="xs:nonNegativeInteger" minOccurs="0"/>
      <xs:element name="class3Value" type="xs:nonNegativeInteger" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="eventBufferOrganizationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="perObjectGroup" type="emptyElement" minOccurs="0"/>
          <xs:element name="perClass" type="classValueType" minOccurs="0"/>
          <xs:element name="singleBufferValue" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventBufferOrganizationType">
    <xs:sequence>
      <xs:element name="capabilities" type="eventBufferOrganizationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="eventBufferOrganizationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.7) Sends Multi-Fragment Responses -->
  <xs:complexType name="sendsMultiFragmentResponsesType">
    <xs:sequence>
      <xs:element name="capabilities" type="yesNoCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.8) Last Fragment Confirmation -->
  <xs:complexType name="requestsLastFragmentConfirmationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="requestsLastFragmentConfirmationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="requestsLastFragmentConfirmationType">
    <xs:sequence>
      <xs:element name="capabilities" type="requestsLastFragmentConfirmationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="requestsLastFragmentConfirmationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.9) DNP Command Settings Preserved Through a Device Reset -->
  <xs:complexType name="settingsPreservedThroughDeviceRestartCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="assignClass" type="emptyElement" minOccurs="0"/>
          <xs:element name="analogDeadbands" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSetPrototypes" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSetDescriptors" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC31ActivateConfiguration" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="settingsPreservedThroughDeviceRestartCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="assignClass" type="emptyElement" minOccurs="0"/>
          <xs:element name="analogDeadbands" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSetPrototypes" type="emptyElement" minOccurs="0"/>
          <xs:element name="dataSetDescriptors" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC31ActivateConfiguration" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="settingsPreservedThroughDeviceRestartType">
    <xs:sequence>
      <xs:element name="capabilities" type="settingsPreservedThroughDeviceRestartCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="settingsPreservedThroughDeviceRestartCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.10) Supports configuration signature -->
  <xs:complexType name="algorithmNameType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
          <xs:sequence>
            <xs:element name="algorithmName" type="xs:normalizedString" minOccurs="1" maxOccurs="unbounded"/>
          </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="configurationSignatureSupportedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="supported" type="algorithmNameType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="configurationSignatureSupportedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="algorithmName" type="xs:string" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="configurationSignatureSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="configurationSignatureSupportedCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="configurationSignatureSupportedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.7.11) Requests Application Confirmation -->
  <xs:complexType name="requestsApplicationConfirmationRadioButtonType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurable" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="requestsApplicationConfirmationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="eventResponses" type="requestsApplicationConfirmationRadioButtonType" minOccurs="0"/>
          <xs:element name="nonFinalFragments" type="requestsApplicationConfirmationRadioButtonType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="requestsApplicationConfirmationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="eventResponses" type="yesNoCurrentValueType" minOccurs="0"/>
          <xs:element name="nonFinalFragments" type="yesNoCurrentValueType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="requestsApplicationConfirmationType">
    <xs:sequence>
      <xs:element name="capabilities" type="requestsApplicationConfirmationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="requestsApplicationConfirmationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>


  <!-- Outstation Capabilities -->
  <xs:complexType name="outstationConfigType">
    <xs:sequence>
      <!-- 1.7.1) Requests Application Layer Confirmation -->
      <xs:element name="applicationLayerConfirmTimeout" type="applicationLayerConfirmTimeoutType" minOccurs="0"/>
      <!-- 1.7.2) How often is Time Synchronization Required from the Master -->
      <xs:element name="timeSyncRequired" type="timeSyncRequiredType" minOccurs="0"/>
      <!-- 1.7.3) Device Trouble Bit IIN 1.6 -->
      <xs:element name="deviceTroubleBit" type="deviceTroubleBitType" minOccurs="0"/>
      <!-- 1.7.4) File Handle Timeout -->
      <xs:element name="fileHandleTimeout" type="fileHandleTimeoutType" minOccurs="0"/>
      <!-- 1.7.5) Event Buffer Overflow Behavior -->
      <xs:element name="eventBufferOverflowBehavior" type="eventBufferOverflowBehaviorType" minOccurs="0"/>
      <!-- 1.7.6) Event Buffer Organization -->
      <xs:element name="eventBufferOrganization" type="eventBufferOrganizationType" minOccurs="0"/>
      <!-- 1.7.7) Sends Multi-Fragment Responses -->
      <xs:element name="sendsMultiFragmentResponses" type="sendsMultiFragmentResponsesType" minOccurs="0"/>
      <!-- 1.7.8) Last Fragment Confirmation -->
      <xs:element name="requestsLastFragmentConfirmation" type="requestsLastFragmentConfirmationType" minOccurs="0"/>
      <!-- 1.7.9) DNP Command Settings Preserved Through a Device Restart -->
      <xs:element name="settingsPreservedThroughDeviceRestart" type="settingsPreservedThroughDeviceRestartType" minOccurs="0"/>
      <!-- 1.7.10) Supports configuration signature -->
      <xs:element name="configurationSignatureSupported" type="configurationSignatureSupportedType" minOccurs="0"/>
      <!-- 1.7.11) Requests Application Confirmation  -->
      <xs:element name="requestsApplicationConfirmation" type="requestsApplicationConfirmationType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ******************************************** -->
  <!-- 1.8) Outstation Unsolicited Response Support -->
  <!-- ******************************************** -->
  <!-- 1.8.1) Supports Unsolicited Reporting -->
  <xs:complexType name="supportsUnsolicitedReportingCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurable" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsUnsolicitedReportingCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="off" type="emptyElement" minOccurs="0"/>
          <xs:element name="on" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="supportsUnsolicitedReportingType">
    <xs:sequence>
      <xs:element name="capabilities" type="supportsUnsolicitedReportingCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="supportsUnsolicitedReportingCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.8.2) Master Data Link Address -->
  <xs:complexType name="masterDataLinkAddressType">
    <xs:sequence>
      <xs:element name="capabilities" type="dataLinkAddressCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="dataLinkAddressCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.8.3) Unsolicited Response Confirmation Timeout -->
  <xs:complexType name="unsolicitedResponseConfirmationTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="variable" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="unsolicitedResponseConfirmationTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="unsolicitedResponseConfirmationTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="unsolicitedResponseConfirmationTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="unsolicitedResponseConfirmationTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.8.4) Number of Unsolicited Retries -->
  <xs:complexType name="maxUnsolicitedRetriesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="infinite" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxUnsolicitedRetriesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="none" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="infinite" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxUnsolicitedRetriesType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxUnsolicitedRetriesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxUnsolicitedRetriesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="unsolicitedConfigType">
    <xs:sequence>
      <!-- 1.8.1) Supports Unsolicited Reporting -->
      <xs:element name="supportsUnsolicitedReporting" type="supportsUnsolicitedReportingType" minOccurs="0"/>
      <!-- 1.8.2) Master Data Link Address -->
      <xs:element name="masterDataLinkAddress" type="masterDataLinkAddressType" minOccurs="0"/>
      <!-- 1.8.3) Unsolicited Response Confirmation Timeout -->
      <xs:element name="unsolicitedResponseConfirmationTimeout" type="unsolicitedResponseConfirmationTimeoutType" minOccurs="0"/>
      <!-- 1.8.4) Number of Unsolicited Retries -->
      <xs:element name="maxUnsolicitedRetries" type="maxUnsolicitedRetriesType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ******************************************************** -->
  <!-- 1.9) Outstation Unsolicited Response Trigger Conditions -->
  <!-- ******************************************************** -->
  <!-- 1.9.1) Number of Class 1 Events -->
  <xs:complexType name="numberOfClassEventsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notUsedToTriggerEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="numberOfClassEventsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notUsedToTriggerEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:positiveInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="numberOfClassOneEventsType">
    <xs:sequence>
      <xs:element name="capabilities" type="numberOfClassEventsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="numberOfClassEventsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.2) Number of Class 2 Events -->
  <xs:complexType name="numberOfClassTwoEventsType">
    <xs:sequence>
      <xs:element name="capabilities" type="numberOfClassEventsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="numberOfClassEventsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.3) Number of Class 3 Events -->
  <xs:complexType name="numberOfClassThreeEventsType">
    <xs:sequence>
      <xs:element name="capabilities" type="numberOfClassEventsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="numberOfClassEventsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.4) Total Number of Events from Any Class -->
  <xs:complexType name="totalNumberOfClassEventsType">
    <xs:sequence>
      <xs:element name="capabilities" type="numberOfClassEventsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="numberOfClassEventsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.5) Hold Time After Class 1 Event (ms) -->
  <xs:complexType name="holdTimeAfterEventCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notUsedToTriggerEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="holdTimeAfterEventCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notUsedToTriggerEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="holdTimeAfterClassOneEventType">
    <xs:sequence>
      <xs:element name="capabilities" type="holdTimeAfterEventCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="holdTimeAfterEventCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.6) Hold Time After Class 2 Event (ms) -->
  <xs:complexType name="holdTimeAfterClassTwoEventType">
    <xs:sequence>
      <xs:element name="capabilities" type="holdTimeAfterEventCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="holdTimeAfterEventCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.7) Hold Time After Class 3 Event (ms) -->
  <xs:complexType name="holdTimeAfterClassThreeEventType">
    <xs:sequence>
      <xs:element name="capabilities" type="holdTimeAfterEventCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="holdTimeAfterEventCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.8) Hold Time After Event Assigned to Any Class -->
  <xs:complexType name="holdTimeAfterAnyEventType">
    <xs:sequence>
      <xs:element name="capabilities" type="holdTimeAfterEventCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="holdTimeAfterEventCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.9) Retrigger Hold Timer -->
  <xs:complexType name="retriggerHoldTimerCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="retriggeredForEachNewEvent" type="emptyElement" minOccurs="0"/>
          <xs:element name="notRetriggeredForEachNewEvent" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="retriggerHoldTimerCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="retriggeredForEachNewEvent" type="emptyElement" minOccurs="0"/>
          <xs:element name="notRetriggeredForEachNewEvent" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="retriggerHoldTimerType">
    <xs:sequence>
      <xs:element name="capabilities" type="retriggerHoldTimerCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="retriggerHoldTimerCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.9.10) Other Unsolicited Response Trigger Conditions -->
  <xs:complexType name="otherTriggerConditionsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="otherTriggerConditionsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="otherTriggerConditionsType">
    <xs:sequence>
      <xs:element name="capabilities" type="otherTriggerConditionsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="otherTriggerConditionsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="unsolicitedResponseTriggerConditionsType">
    <xs:sequence>
      <!-- 1.9.1) Number of Class 1 Events -->
      <xs:element name="numberOfClassOneEvents" type="numberOfClassOneEventsType" minOccurs="0"/>
      <!-- 1.9.2) Number of Class 2 Events -->
      <xs:element name="numberOfClassTwoEvents" type="numberOfClassTwoEventsType" minOccurs="0"/>
      <!-- 1.9.3) Number of Class 3 Events -->
      <xs:element name="numberOfClassThreeEvents" type="numberOfClassThreeEventsType" minOccurs="0"/>
      <!-- 1.9.4) Total Number of Events from Any Class -->
      <xs:element name="totalNumberOfClassEvents" type="totalNumberOfClassEventsType" minOccurs="0"/>
      <!-- 1.9.5) Hold Time After Class 1 Event (ms) -->
      <xs:element name="holdTimeAfterClassOneEvent" type="holdTimeAfterClassOneEventType" minOccurs="0"/>
      <!-- 1.9.6) Hold Time After Class 2 Event (ms) -->
      <xs:element name="holdTimeAfterClassTwoEvent" type="holdTimeAfterClassTwoEventType" minOccurs="0"/>
      <!-- 1.9.7) Hold Time After Class 3 Event (ms) -->
      <xs:element name="holdTimeAfterClassThreeEvent" type="holdTimeAfterClassThreeEventType" minOccurs="0"/>
      <!-- 1.9.8) Hold Time After Event Assigned to Any Class -->
      <xs:element name="holdTimeAfterAnyEvent" type="holdTimeAfterAnyEventType" minOccurs="0"/>
      <!-- 1.9.9) Retrigger Hold Timer -->
      <xs:element name="retriggerHoldTimer" type="retriggerHoldTimerType" minOccurs="0"/>
      <!-- 1.9.10) Other Unsolicited Response Trigger Conditions -->
      <xs:element name="otherTriggerConditions" type="otherTriggerConditionsType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- **************************** -->
  <!-- 1.10) Outstation Performance -->
  <!-- **************************** -->
  <xs:complexType name="timingRangeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:double" minOccurs="0"/>
          <xs:element name="range" type="doubleRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:double" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="other" type="notConfigurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timingRangeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:double" minOccurs="0"/>
          <xs:element name="other" type="notConfigurableCustomType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timingPerformanceCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="other" type="notConfigurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timingPerformanceCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="other" type="notConfigurableCustomType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="timingRangeType">
    <xs:sequence>
      <xs:element name="capabilities" type="timingRangeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="timingRangeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="timingPerformanceType">
    <xs:sequence>
      <xs:element name="capabilities" type="timingPerformanceCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="timingPerformanceCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.10.2) When Does Outstation Set IIN 1.4 -->
  <xs:complexType name="outstationSetsIIN14CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="atStartup" type="emptyElement" minOccurs="0"/>
          <xs:element name="periodicallyFixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="periodicallyRange" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="periodicallySelectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="afterLastTimeSyncFixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="afterLastTimeSyncRange" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="afterLastTimeSyncSelectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="whenTimeErrorExceedsFixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="whenTimeErrorExceedsRange" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="whenTimeErrorExceedsSelectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="outstationSetsIIN14CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:sequence>
            <xs:element name="atStartup" type="emptyElement" minOccurs="0"/>
            <xs:element name="periodically" type="xs:positiveInteger" minOccurs="0"/>
            <xs:element name="afterLastTimeSync" type="xs:positiveInteger" minOccurs="0"/>
            <xs:element name="whenTimeErrorExceeds" type="xs:positiveInteger" minOccurs="0"/>
          </xs:sequence>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="outstationSetsIIN14Type">
    <xs:sequence>
      <xs:element name="capabilities" type="outstationSetsIIN14CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="outstationSetsIIN14CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="outstationPerformanceType">
    <xs:sequence>
      <!-- 1.10.1) Maximum Time Base Drift (milliseconds per minute) -->
      <xs:element name="maxTimeBaseDrift" type="timingRangeType" minOccurs="0"/>
      <!-- 1.10.2) When Does Outstation Set IIN 1.4? -->
      <xs:element name="outstationSetsIIN14" type="outstationSetsIIN14Type" minOccurs="0"/>
      <!-- 1.10.3) Maximum Internal Time Reference Error When et Via DNP (ms) -->
      <xs:element name="referenceErrorViaDNP" type="timingRangeType" minOccurs="0"/>
      <!-- 1.10.4) Maximum Delay Measurement Error (ms) -->
      <xs:element name="delayMeasurementError" type="timingPerformanceType" minOccurs="0"/>
      <!-- 1.10.5) Maximum Response Time (ms) -->
      <xs:element name="responseTime" type="timingPerformanceType" minOccurs="0"/>
      <!-- 1.10.6) Maximum Time From Startup to IIN 1.4 Assertion (ms) -->
      <xs:element name="startupToIIN14" type="timingPerformanceType" minOccurs="0"/>
      <!-- 1.10.7) Maximum Event Time-tag Error for Local Binary and Double Bit I/O (ms) -->
      <xs:element name="binaryOrDoubleBitEventError" type="timingPerformanceType" minOccurs="0"/>
      <!-- 1.10.8) Maximum Event Time-tag Error for Local I/O Other Than Binary and Double Bit Data Types (ms) -->
      <xs:element name="nonBinaryOrDoubleBitEventError" type="timingPerformanceType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- *************************************************** -->
  <!-- 1.11) Record Individual Field Outstation Parameters -->
  <!-- *************************************************** -->
  <xs:complexType name="configurationStringType">
    <xs:sequence>
      <xs:element name="currentValue" type="xs:string" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="fieldConfigType">
    <xs:sequence>
      <!-- 1.11.1) Location Name Or Code -->
      <xs:element name="outstationLocation" type="configurationStringType" minOccurs="0"/>
      <!-- 1.11.2) Outstation Field ID Code/Number -->
      <xs:element name="outstationId" type="configurationStringType" minOccurs="0"/>
      <!-- 1.11.3) Outstation Name -->
      <xs:element name="outstationName" type="configurationStringType" minOccurs="0"/>
      <!-- 1.11.4) Device Serial Number -->
      <xs:element name="deviceSerialNumber" type="configurationStringType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- *************************************************** -->
  <!-- 1.12) Security Parameters                           -->
  <!-- *************************************************** -->
  <!-- 1.12.1) DNP3 device support for secure authenticationn -->
  <xs:complexType name="secureAuthenticationSupportedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="version" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
                <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="secureAuthenticationSupportedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notSupported" type="emptyElement" minOccurs="0"/>
          <xs:element name="version" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="secureAuthenticationSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="secureAuthenticationSupportedCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="secureAuthenticationSupportedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.2) User number -->
  <xs:complexType name="maxUsersCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="maxUsers" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxUsersCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="maxUsers" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxNumberUsersType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxUsersCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxUsersCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.3) Security message response timeout -->
  <xs:complexType name="securityResponseTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="securityResponseTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:nonNegativeInteger">
                <xs:maxInclusive value="120000"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="securityResponseTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="securityResponseTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="securityResponseTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.4) Aggressive mode of operation (receive) -->
  <xs:complexType name="acceptsAggressiveModeType">
    <xs:sequence>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.5) Aggressive mode of operation (issuing) -->
  <xs:complexType name="issuesAggressiveModeType">
    <xs:sequence>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.6) Session key change interval -->
  <xs:complexType name="sessionKeyChangeIntervalCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="canBeDisabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sessionKeyChangeIntervalCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="isDisabled" minOccurs="0"/>
          <xs:element name="value" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:nonNegativeInteger">
                <xs:maxInclusive value="7200"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sessionKeyChangeIntervalType">
    <xs:sequence>
      <xs:element name="capabilities" type="sessionKeyChangeIntervalCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sessionKeyChangeIntervalCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.7) Session key change message count -->
  <xs:complexType name="sessionKeyChangeMessageCountCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sessionKeyChangeMessageCountCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:nonNegativeInteger">
                <xs:maxInclusive value="10000"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sessionKeyChangeMessageCountType">
    <xs:sequence>
      <xs:element name="capabilities" type="sessionKeyChangeMessageCountCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sessionKeyChangeMessageCountCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.8) Maximum error count -->
  <xs:complexType name="maxErrorCountCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxErrorCountCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" minOccurs="0">
            <xs:simpleType>
              <xs:restriction base="xs:nonNegativeInteger">
                <xs:maxInclusive value="10"/>
              </xs:restriction>
            </xs:simpleType>
          </xs:element>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxErrorCountType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxErrorCountCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxErrorCountCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.9) MAC algorithm requested in a challenge exchange -->
  <xs:complexType name="macAlgorithmRequestedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="sha1Truncated4" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha1Truncated8" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha1Truncated10" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha256Truncated8" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha256Truncated16" type="emptyElement" minOccurs="0"/>
          <xs:element name="aesGMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="macAlgorithmRequestedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="sha1Truncated4" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha1Truncated8" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha1Truncated10" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha256Truncated8" type="emptyElement" minOccurs="0"/>
          <xs:element name="sha256Truncated16" type="emptyElement" minOccurs="0"/>
          <xs:element name="aesGMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="macAlgorithmRequestedType">
    <xs:sequence>
      <xs:element name="capabilities" type="macAlgorithmRequestedCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="macAlgorithmRequestedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.10) Key wrap algorithm -->
  <xs:complexType name="keyWrapAlgorithmCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="aes128" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes256" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="keyWrapAlgorithmCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="aes128" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes256" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="keyWrapAlgorithmType">
    <xs:sequence>
      <xs:element name="capabilities" type="keyWrapAlgorithmCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="keyWrapAlgorithmCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.11) Cipher Suites used with DNP implementations using TLS  -->
  <xs:complexType name="TLSCipherSuitesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncryptedRC4" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHESignedDSSEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHESignedRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncryptedAES256" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHEncryptedAES256" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TLSCipherSuitesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncryptedRC4" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHESignedDSSEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHESignedRSAEncrypted3DES" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHSignedDSSEncryptedAES256" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHEncryptedAES128" type="emptyElement" minOccurs="0"/>
          <xs:element name="TLSDHEncryptedAES256" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="TLSCipherSuitesType">
    <xs:sequence>
      <xs:element name="capabilities" type="TLSCipherSuitesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="TLSCipherSuitesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.12) Change cipher request timeout  -->
  <xs:complexType name="changeCipherRequestTimeoutCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="changeCipherRequestTimeoutCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="changeCipherRequestTimeoutType">
    <xs:sequence>
      <xs:element name="capabilities" type="changeCipherRequestTimeoutCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="changeCipherRequestTimeoutCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.13) Number of Certificate Authorities supported -->
  <xs:complexType name="numberCASupportedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="numberCASupportedType">
    <xs:sequence>
      <xs:element name="currentValue" type="numberCASupportedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.14) Certificate Revocation check time:  -->
  <xs:complexType name="certificateRevocationCheckTimeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="certificateRevocationCheckTimeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="certificateRevocationCheckTimeType">
    <xs:sequence>
      <xs:element name="capabilities" type="certificateRevocationCheckTimeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="certificateRevocationCheckTimeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.15) Additional critical function codes -->
  <xs:complexType name="additionalCriticalFCsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="FC0" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC1" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC7" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC8" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC9" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC10" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC11" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC12" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC22" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC23" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC25" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC26" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC27" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC28" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC30" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC129" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC130" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="additionalCriticalFCsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="FC0" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC1" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC7" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC8" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC9" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC10" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC11" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC12" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC22" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC23" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC25" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC26" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC27" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC28" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC30" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC129" type="emptyElement" minOccurs="0"/>
          <xs:element name="FC130" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="additionalCriticalFCsType">
    <xs:sequence>
      <xs:element name="capabilities" type="additionalCriticalFCsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="additionalCriticalFCsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.16) Other critical fragments -->
  <xs:complexType name="criticalFragmentsType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="criticalFragments" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="otherCriticalFragmentsType">
    <xs:sequence>
      <xs:element name="capabilities" type="criticalFragmentsType" minOccurs="0"/>
      <xs:element name="currentValue" type="criticalFragmentsType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.17) Support for remote update key changes -->
  <xs:complexType name="bySymmetricCryptographyCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="supported" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes-128KeyWrapWithSHA-1-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes-256KeyWrapWithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes-256KeyWrapWithAES-GMAC" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="bySymmetricCryptographyCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="aes-128KeyWrapWithSHA-1-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes-256KeyWrapWithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="aes-256KeyWrapWithAES-GMAC" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byAsymmetricCryptographyCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="supported" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-1024WithSHA-1-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-2048WithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-3072WithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-2048WithAES-GMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-3072WithAES-GMAC" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byAsymmetricCryptographyCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="RSAES-OAEP-1024WithSHA-1-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-2048WithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-3072WithSHA-256-HMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-2048WithAES-GMAC" type="emptyElement" minOccurs="0"/>
          <xs:element name="RSAES-OAEP-3072WithAES-GMAC" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="remoteUpdateKeyChangeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="bySymmetricCryptography" type="bySymmetricCryptographyCapabilitiesType" minOccurs="0"/>
          <xs:element name="byAsymmetricCryptography" type="byAsymmetricCryptographyCapabilitiesType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="remoteUpdateKeyChangeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="bySymmetricCryptography" type="bySymmetricCryptographyCurrentValueType" minOccurs="0"/>
          <xs:element name="byAsymmetricCryptography" type="byAsymmetricCryptographyCurrentValueType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="remoteUpdateKeyChangeSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="remoteUpdateKeyChangeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="remoteUpdateKeyChangeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.12.18)  Default user credentials are permitted to expire: -->
  <xs:complexType name="permitUserCredentialExpiryType">
    <xs:sequence>
      <xs:element name="capabilities" type="yesNoCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="securityConfigType">
    <xs:sequence>
      <!-- 1.12.1) DNP3 device support for secure authentication -->
      <xs:element name="secureAuthenticationSupported" type="secureAuthenticationSupportedType" minOccurs="0"/>
      <!-- 1.12.2) Maximum number of users -->
      <xs:element name="maxNumberUsers" type="maxNumberUsersType" minOccurs="0"/>
      <!-- 1.12.3) Security message response timeout -->
      <xs:element name="securityResponseTimeout" type="securityResponseTimeoutType" minOccurs="0"/>
      <!-- 1.12.4) Aggressive mode of operation (receive) -->
      <xs:element name="acceptsAggressiveMode" type="acceptsAggressiveModeType" minOccurs="0"/>
      <!-- 1.12.5) Aggressive mode of operation (issuing) -->
      <xs:element name="issuesAggressiveMode" type="issuesAggressiveModeType" minOccurs="0"/>
      <!-- 1.12.6) Session key change interval -->
      <xs:element name="sessionKeyChangeInterval" type="sessionKeyChangeIntervalType" minOccurs="0"/>
      <!-- 1.12.7) Session key change message count -->
      <xs:element name="sessionKeyChangeMessageCount" type="sessionKeyChangeMessageCountType" minOccurs="0"/>
      <!-- 1.12.8) Maximum error count -->
      <xs:element name="maxErrorCount" type="maxErrorCountType" minOccurs="0"/>
      <!-- 1.12.9) HMAC algorithm requested in a challenge exchange -->
      <xs:element name="macAlgorithmRequested" type="macAlgorithmRequestedType" minOccurs="0"/>
      <!-- 1.12.10) Key-wrap algorithm to encrypt session keys -->
      <xs:element name="keyWrapAlgorithm" type="keyWrapAlgorithmType" minOccurs="0"/>
      <!-- 1.12.11) Cipher Suites used with DNP implementations using TLS -->
      <xs:element name="TLSCipherSuites" type="TLSCipherSuitesType" minOccurs="0"/>
      <!-- 1.12.12) Change cipher request timeout -->
      <xs:element name="changeCipherRequestTimeout" type="changeCipherRequestTimeoutType" minOccurs="0"/>
      <!-- 1.12.13) Number of Certificate Authorities supported -->
      <xs:element name="numberCASupported" type="numberCASupportedType" minOccurs="0"/>
      <!-- 1.12.14) Certificate Revocation check time -->
      <xs:element name="certificateRevocationCheckTime" type="certificateRevocationCheckTimeType" minOccurs="0"/>
      <!-- 1.12.15) Additional critical function codes -->
      <xs:element name="additionalCriticalFCs" type="additionalCriticalFCsType" minOccurs="0"/>
      <!-- 1.12.16) Other critical fragments -->
      <xs:element name="otherCriticalFragments" type="otherCriticalFragmentsType" minOccurs="0"/>
      <!-- 1.12.17) Support for remote update key changes -->
      <xs:element name="remoteUpdateKeyChangeSupported" type="remoteUpdateKeyChangeSupportedType" minOccurs="0"/>
      <!-- 1.12.18) “Default” user credentials are permitted to expire: -->
      <xs:element name="permitUserCredentialExpiry" type="permitUserCredentialExpiryType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

    <!-- 1.13.1) Support for broadcast functionality -->
  <xs:complexType name="broadcastFunctionalitySupportedCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurable" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalitySupportedCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalitySupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalitySupportedCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalitySupportedCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.2) Write functions (FC = 2) supported with broadcast requests -->
  <xs:complexType name="writeClockCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeClockCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeLastRecordedTimeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeLastRecordedTimeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="clearRestartCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="clearRestartCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeAnyOtherCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeAnyOtherCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="writeClockType">
    <xs:sequence>
      <xs:element name="capabilities" type="writeClockCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="writeClockCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="clearRestartType">
    <xs:sequence>
      <xs:element name="capabilities" type="clearRestartCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="clearRestartCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="writeLastRecordedTimeType">
    <xs:sequence>
      <xs:element name="capabilities" type="writeLastRecordedTimeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="writeLastRecordedTimeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="writeAnyOtherType">
    <xs:sequence>
      <xs:element name="capabilities" type="writeAnyOtherCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="writeAnyOtherCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC2Type">
    <xs:sequence>
      <xs:element name="writeClock" type="writeClockType" minOccurs="0"/>
      <xs:element name="writeLastRecordedTime" type="writeLastRecordedTimeType" minOccurs="0"/>
      <xs:element name="clearRestart" type="clearRestartType" minOccurs="0"/>
      <xs:element name="writeAnyOther" type="writeAnyOtherType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.3) Direct operate functions (FC = 5) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC5CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC5CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC5Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC5CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC5CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.4) Direct operate, no acknowledgement functions (FC = 6) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC6CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC6CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC6Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC6CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC6CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.5) Immediate freeze functions (FC = 7) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC7CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC7CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC7Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC7CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC7CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.6) Immediate freeze, no acknowledgement functions (FC = 8) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC8CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC8CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC8Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC8CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC8CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.7) Freeze and clear functions (FC = 9) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC9CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC9CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC9Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC9CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC9CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.8) Freeze and clear, no acknowledgement functions (FC = 10) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC10CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC10CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC10Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC10CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC10CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.9) Freeze at time functions (FC = 11) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC11CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC11CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC11Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC11CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC11CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.10) Freeze at time, no acknowledgement functions (FC = 12) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC12CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC12CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC12Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC12CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC12CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.11) Cold restart functions (FC = 13) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC13CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC13CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC13Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC13CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC13CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.12) Warm restart functions (FC = 14) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC14CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC14CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC14Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC14CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC14CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.13) Initialize data functions (FC = 15) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC15CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC15CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC15Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC15CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC15CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.14) Initialize application functions (FC = 16) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC16CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC16CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC16Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC16CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC16CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.15) Start application functions (FC = 17) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC17CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC17CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC17Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC17CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC17CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.16) Stop application functions (FC = 18) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC18CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC18CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC18Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC18CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC18CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.17) Save configuration functions (FC = 19) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC19CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC19CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC19Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC19CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC19CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.18) Enable unsolicited functions (FC = 20) supported with broadcast requests -->
  <xs:complexType name="byEventClassCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byEventClassCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byAnyOtherCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byAnyOtherCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="byEventClassType">
    <xs:sequence>
      <xs:element name="capabilities" type="byEventClassCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="byEventClassCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="byAnyOtherType">
    <xs:sequence>
      <xs:element name="capabilities" type="byAnyOtherCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="byAnyOtherCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC20Type">
    <xs:sequence>
      <xs:element name="byEventClass" type="byEventClassType" minOccurs="0"/>
      <xs:element name="byAnyOther" type="byAnyOtherType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.19) Disable unsolicitedn functions (FC = 21) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC21Type">
    <xs:sequence>
      <xs:element name="byEventClass" type="byEventClassType" minOccurs="0"/>
      <xs:element name="byAnyOther" type="byAnyOtherType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.20) Assign class functions (FC = 22) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC22CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC22CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC22Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC22CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC22CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.21) Record current time functions (FC = 24) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC24CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC24CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC24Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC24CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC24CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 1.13.22) Activate configuration functions (FC = 31) supported with broadcast requests -->
  <xs:complexType name="broadcastFunctionalityFC31CapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableOther" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC31CurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="disabled" type="emptyElement" minOccurs="0"/>
          <xs:element name="enabled" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="broadcastFunctionalityFC31Type">
    <xs:sequence>
      <xs:element name="capabilities" type="broadcastFunctionalityFC31CapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="broadcastFunctionalityFC31CurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="broadcastConfigType">
    <xs:sequence>
      <!-- 1.13.1) Support for broadcast functionality -->
      <xs:element name="broadcastFunctionalitySupported" type="broadcastFunctionalitySupportedType" minOccurs="0"/>
      <!-- 1.13.2) Write functions (FC = 2) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC2Supported" type="broadcastFunctionalityFC2Type" minOccurs="0"/>
      <!-- 1.13.3) Direct operate functions (FC = 5) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC5Supported" type="broadcastFunctionalityFC5Type" minOccurs="0"/>
      <!-- 1.13.4) Direct operate, no acknowledgement functions (FC = 6) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC6Supported" type="broadcastFunctionalityFC6Type" minOccurs="0"/>
      <!-- 1.13.5) Immediate freeze functions (FC = 7) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC7Supported" type="broadcastFunctionalityFC7Type" minOccurs="0"/>
      <!-- 1.13.6) Immediate freeze, no acknowledgement functions (FC = 8) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC8Supported" type="broadcastFunctionalityFC8Type" minOccurs="0"/>
      <!-- 1.13.7) Freeze and clear functions (FC = 9) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC9Supported" type="broadcastFunctionalityFC9Type" minOccurs="0"/>
      <!-- 1.13.8) Freeze and clear, no acknowledgement functions (FC = 10) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC10Supported" type="broadcastFunctionalityFC10Type" minOccurs="0"/>
      <!-- 1.13.9) Freeze at time functions (FC = 11) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC11Supported" type="broadcastFunctionalityFC11Type" minOccurs="0"/>
      <!-- 1.13.10) Freeze at time, no acknowledgement functions (FC = 12) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC12Supported" type="broadcastFunctionalityFC12Type" minOccurs="0"/>
      <!-- 1.13.11) Cold restart functions (FC = 13) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC13Supported" type="broadcastFunctionalityFC13Type" minOccurs="0"/>
      <!-- 1.13.12) Warm restart functions (FC = 14) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC14Supported" type="broadcastFunctionalityFC14Type" minOccurs="0"/>
      <!-- 1.13.13) Initialize data functions (FC = 15) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC15Supported" type="broadcastFunctionalityFC15Type" minOccurs="0"/>
      <!-- 1.13.14) Initialize application functions (FC = 16) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC16Supported" type="broadcastFunctionalityFC16Type" minOccurs="0"/>
      <!-- 1.13.15) Start application functions (FC = 17) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC17Supported" type="broadcastFunctionalityFC17Type" minOccurs="0"/>
      <!-- 1.13.16) Stop application functions (FC = 18) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC18Supported" type="broadcastFunctionalityFC18Type" minOccurs="0"/>
      <!-- 1.13.17) Save configuration functions (FC = 19) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC19Supported" type="broadcastFunctionalityFC19Type" minOccurs="0"/>
      <!-- 1.13.18) Enable unsolicitedn functions (FC = 20) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC20Supported" type="broadcastFunctionalityFC20Type" minOccurs="0"/>
      <!-- 1.13.19) Disable unsolicitedn functions (FC = 21) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC21Supported" type="broadcastFunctionalityFC21Type" minOccurs="0"/>
      <!-- 1.13.20) Assign class functions (FC = 22) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC22Supported" type="broadcastFunctionalityFC22Type" minOccurs="0"/>
      <!-- 1.13.21) Record current time functions (FC = 24) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC24Supported" type="broadcastFunctionalityFC24Type" minOccurs="0"/>
      <!-- 1.13.22) Activate configuration functions (FC = 31) supported with broadcast requests -->
      <xs:element name="broadcastFunctionalityFC31Supported" type="broadcastFunctionalityFC31Type" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ************************************* -->
  <!-- 2) Mapping to IEC 61850 Object Models -->
  <!-- ************************************* -->
  <!-- ************************************ -->
  <!-- 2.1) Device Level IEC 61850 Mapping -->
  <!-- ************************************ -->
  <!-- IEC 61850 Base Types -->
  <xs:simpleType name="predefinedCDCEnum">
    <xs:restriction base="xs:Name">
      <xs:enumeration value="SPS"/>
      <xs:enumeration value="DPS"/>
      <xs:enumeration value="INS"/>
      <xs:enumeration value="ACT"/>
      <xs:enumeration value="ACD"/>
      <xs:enumeration value="SEC"/>
      <xs:enumeration value="BCR"/>
      <xs:enumeration value="MV"/>
      <xs:enumeration value="CMV"/>
      <xs:enumeration value="SAV"/>
      <xs:enumeration value="WYE"/>
      <xs:enumeration value="DEL"/>
      <xs:enumeration value="SEQ"/>
      <xs:enumeration value="HMV"/>
      <xs:enumeration value="HWYE"/>
      <xs:enumeration value="HDEL"/>
      <xs:enumeration value="SPC"/>
      <xs:enumeration value="DPC"/>
      <xs:enumeration value="INC"/>
      <xs:enumeration value="BSC"/>
      <xs:enumeration value="ISC"/>
      <xs:enumeration value="APC"/>
      <xs:enumeration value="SPG"/>
      <xs:enumeration value="ING"/>
      <xs:enumeration value="ASG"/>
      <xs:enumeration value="CURVE"/>
      <xs:enumeration value="DPL"/>
      <xs:enumeration value="LPL"/>
      <xs:enumeration value="CSD"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="extensionCDCEnum">
    <xs:restriction base="xs:Name">
      <xs:minLength value="1"/>
      <xs:pattern value="\p{Lu}+"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="cdcEnum">
    <xs:union memberTypes="predefinedCDCEnum extensionCDCEnum"/>
  </xs:simpleType>
  <!-- <xs:simpleType name="trgOptEnum">
    <xs:restriction base="xs:Name">
      <xs:enumeration value="dchg"/>
      <xs:enumeration value="qchg"/>
      <xs:enumeration value="dupd"/>
      <xs:enumeration value="period"/>
      <xs:enumeration value="none"/>
    </xs:restriction>
  </xs:simpleType> -->
  <xs:simpleType name="fcEnum">
    <xs:restriction base="xs:Name">
      <xs:enumeration value="ST"/>
      <xs:enumeration value="MX"/>
      <xs:enumeration value="CO"/>
      <xs:enumeration value="SP"/>
      <xs:enumeration value="SG"/>
      <xs:enumeration value="SE"/>
      <xs:enumeration value="SV"/>
      <xs:enumeration value="CF"/>
      <xs:enumeration value="DC"/>
      <xs:enumeration value="EX"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="predefinedBasicTypeEnum">
    <xs:restriction base="xs:Name">
      <xs:enumeration value="BOOLEAN"/>
      <xs:enumeration value="INT8"/>
      <xs:enumeration value="INT16"/>
      <xs:enumeration value="INT24"/>
      <xs:enumeration value="INT32"/>
      <xs:enumeration value="INT128"/>
      <xs:enumeration value="INT8U"/>
      <xs:enumeration value="INT16U"/>
      <xs:enumeration value="INT24U"/>
      <xs:enumeration value="INT32U"/>
      <xs:enumeration value="FLOAT32"/>
      <xs:enumeration value="FLOAT64"/>
      <xs:enumeration value="Enum"/>
      <xs:enumeration value="Dbpos"/>
      <xs:enumeration value="Tcmd"/>
      <xs:enumeration value="Quality"/>
      <xs:enumeration value="Timestamp"/>
      <xs:enumeration value="VisString32"/>
      <xs:enumeration value="VisString64"/>
      <xs:enumeration value="VisString255"/>
      <xs:enumeration value="Octet64"/>
      <xs:enumeration value="Struct"/>
      <xs:enumeration value="EntryTime"/>
      <xs:enumeration value="Unicode255"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="extensionBasicTypeEnum">
    <xs:restriction base="xs:Name">
      <xs:pattern value="\p{Lu}[\p{L},\d]*"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="basicTypeEnum">
    <xs:union memberTypes="predefinedBasicTypeEnum extensionBasicTypeEnum"/>
  </xs:simpleType>
  <xs:complexType name="enumValueType">
    <xs:simpleContent>
      <xs:extension base="xs:normalizedString">
        <xs:attribute name="ord" type="xs:integer" use="required"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="enumType">
    <xs:sequence>
      <xs:element name="enumVal" type="enumValueType" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="id" type="xs:normalizedString"/>
  </xs:complexType>
  <!-- DNP3 XPath Type -->
  <xs:complexType name="dnp3XPathType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="name" type="xs:string" use="optional"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <!-- IEC 61850 Path Type -->
  <xs:simpleType name="iec61850PathPatternType">
    <xs:restriction base="xs:string">
      <xs:pattern value="(\p{L}[\p{L}\d]*){0,1}/(\p{L}[\p{L}\d]*(\(\d+\)){0,1}.){2,}\p{L}[\p{L}\d]*"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="iec61850PathType">
    <xs:simpleContent>
      <xs:extension base="iec61850PathPatternType">
        <xs:attribute name="name" type="xs:string" use="optional"/>
        <xs:attribute name="fc" type="fcEnum"/>
        <xs:attribute name="dataType" type="basicTypeEnum"/>
        <xs:attribute name="cdc" type="cdcEnum"/>
        <xs:attribute name="enumTypeId" type="xs:normalizedString" use="optional"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <!-- Mapping Rules -->
  <xs:simpleType name="mappingRuleBasicType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="BOOLEAN_TO_BI"/>
      <xs:enumeration value="BOOLEAN_TO_BO"/>
      <xs:enumeration value="BOOLEAN_TO_BO_CONST"/>
      <xs:enumeration value="CURRENCY_TO_OCT"/>
      <xs:enumeration value="DESC_TO_PROFILE"/>
      <xs:enumeration value="DPS_TO_2_BI"/>
      <xs:enumeration value="DPS_TO_DBBI"/>
      <xs:enumeration value="DPS_TO_BO"/>
      <xs:enumeration value="ENUM_HVREF_TO_PROFILE"/>
      <xs:enumeration value="ENUM_TO_AI"/>
      <xs:enumeration value="ENUM_TO_AO"/>
      <xs:enumeration value="ENUM_TO_AO_CONST"/>
      <xs:enumeration value="ENUM_TO_BI"/>
      <xs:enumeration value="ENUM_TO_BO"/>
      <xs:enumeration value="ENUM_TO_BO_PROFILE"/>
      <xs:enumeration value="ENUM_TO_DBBI"/>
      <xs:enumeration value="FLOAT_TO_AI"/>
      <xs:enumeration value="FLOAT_TO_AI_FP"/>
      <xs:enumeration value="FLOAT_TO_AO"/>
      <xs:enumeration value="FLOAT_TO_AO_FP"/>
      <xs:enumeration value="FLOAT_TO_AO_FP_CONST"/>
      <xs:enumeration value="FRZ_ENA_TO_FRZWT"/>
      <xs:enumeration value="FRZ_PD_TO_TODINT"/>
      <xs:enumeration value="INT_RMS_CYC_TO_PROFILE"/>
      <xs:enumeration value="INT_TO_AI"/>
      <xs:enumeration value="INT_TO_AI_CONST"/>
      <xs:enumeration value="INT_TO_AI_DEADBAND"/>
      <xs:enumeration value="INT_TO_AI_FP"/>
      <xs:enumeration value="INT_TO_AO"/>
      <xs:enumeration value="INT_TO_AO_CONST"/>
      <xs:enumeration value="INT_TO_AO_FP"/>
      <xs:enumeration value="INT_TO_AO_PROFILE"/>
      <xs:enumeration value="INT_TO_CTR"/>
      <xs:enumeration value="INT_TO_FRZ_CTR"/>
      <xs:enumeration value="MULTI_CELL_TO_AIFP"/>
      <xs:enumeration value="MULTI_INT_TO_AI"/>
      <xs:enumeration value="OCT_TO_OCT"/>
      <xs:enumeration value="PULSE_CONFIG_TO_CROB"/>
      <xs:enumeration value="QUALITY_TO_BIN_FLAG"/>
      <xs:enumeration value="QUALITY_TO_CTR_FLAG"/>
      <xs:enumeration value="QUALITY_TO_ANA_FLAG"/>
      <xs:enumeration value="QUALITY_TO_OCT"/>
      <xs:enumeration value="STRING_TO_OCT "/>
      <xs:enumeration value="TIME_AND_DATE_TO_AO"/>
      <xs:enumeration value="TIME_IEC_ONLY"/>
      <xs:enumeration value="TIME_TO_AO"/>
      <xs:enumeration value="TIME_TO_FRZ_CTR"/>
      <xs:enumeration value="TIME_TO_TIME"/>
      <xs:enumeration value="UNITS_TO_AI_CONST"/>
      <xs:enumeration value="UNITS_TO_OCT"/>
      <xs:enumeration value="UNITS_TO_PROFILE"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="mappingRuleExtensionType">
    <xs:restriction base="xs:Name">
      <xs:pattern value="\p{Lu}[\p{Lu},_]*"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="mappingRuleType">
    <xs:union memberTypes="mappingRuleBasicType mappingRuleExtensionType"/>
  </xs:simpleType>
  <!-- 2.1.1) Access Point -->
  <!-- 2.1.2) Deprecated, use dnpToIEC61850Mapping 
  <xs:complexType name="iec61850MappingType">
    <xs:sequence>
      <xs:element name="note" type="xs:string" minOccurs="0"/>
      <xs:element name="dnp3XPath" type="xs:string"/>
      <xs:element name="iec61850Path" type="xs:string"/>
      <xs:element name="functionalConstraint" type="fcEnum"/>
      <xs:element name="dataType" type="basicTypeEnum"/>
      <xs:element name="commonDataClass" type="cdcEnum"/>
      <xs:element name="triggerOptions" type="trgOptEnum" minOccurs="0"/>
      <xs:element name="enumTypeId" type="xs:normalizedString" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType> -->
  <!--  2.1.3) Mapping to use when mapping to/from IEC 61850 using predefined rules -->
  <xs:complexType name="iec61850RuleMappingType">
    <xs:sequence>
      <!-- Note or annotation -->
      <xs:element name="note" type="xs:string" minOccurs="0"/>
      <!-- Rule to use to map DNP3 element(s) to/from IEC 61850 data attribute(s) -->
      <xs:element name="rule" type="mappingRuleType"/>
      <!-- Input DNP3 data point(s) or parameters being mapped from -->
      <xs:element name="dnp3XPath" type="dnp3XPathType" minOccurs="1" maxOccurs="unbounded"/>
      <!-- Output IEC 61850 Data Attribute(s) being mapped to -->
      <xs:element name="iec61850Path" type="iec61850PathType" minOccurs="1" maxOccurs="1"/>
    </xs:sequence>
  </xs:complexType>
  <!--  2.1.4) Mapping to use when mapping to/from IEC 61850 using equations -->
  <xs:complexType name="iec61850EquationMappingType">
    <xs:sequence>
      <!-- Note or annotation -->
      <xs:element name="note" type="xs:string" minOccurs="0"/>
      <!-- Equation to use to map DNP3 element(s) to/from IEC 61850 data attribute(s) -->
      <xs:element name="equation" type="xs:string"/>
      <!-- Input DNP3 data point(s) or parameters being mapped from -->
      <xs:element name="dnp3XPath" type="dnp3XPathType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- Output IEC 61850 Data Attribute(s) being mapped to -->
      <xs:element name="iec61850Path" type="iec61850PathType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="iec61850DeviceMappingType">
    <xs:sequence>
      <!--  2.1.1) Access Point, Deprecated, Do Not Use -->
      <xs:element name="accessPoint" type="xs:string" minOccurs="0"/>
      <!--  2.1.2) Deprecated, use iec61850RuleMapping or iec61850EquationMapping 
      <xs:element name="iec61850Mapping" type="iec61850MappingType" minOccurs="0" maxOccurs="unbounded"/> -->
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <!-- 2.1.3) Use this element when mapping to/from iec61850 using one -->
        <!-- of the predefined rules in IEC 1815.1 Mapping is bi-directional -->
        <xs:element name="iec61850RuleMapping" type="iec61850RuleMappingType"/>
        <!-- 2.1.4) Use this element when mapping to/from iec61850 using an -->
        <!-- equation to map 0 or more input parameters to a single output -->
        <!-- parameter. Direction of mapping is determined by the variable -->
        <!-- on the left hand side of the equation -->
        <xs:element name="iec61850EquationMapping" type="iec61850EquationMappingType"/>
      </xs:choice>
      <!-- Enum Definitions -->
      <xs:element name="enum" type="enumType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ******************************************************** -->
  <!-- 3) Capabilities and Current Settings for Device Database -->
  <!-- ******************************************************** -->
  <!-- Define base type for all data points -->
  <xs:complexType name="pointType">
    <xs:sequence>
      <xs:element name="index" type="xs:nonNegativeInteger"/>
      <xs:element name="name" type="xs:string" minOccurs="0"/>
      <xs:element name="description" type="xs:string" minOccurs="0"/>
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Define base type for all input data points -->
  <xs:complexType name="inputPointType">
    <xs:complexContent>
      <xs:extension base="pointType">
        <xs:sequence>
          <xs:element name="changeEventClass" type="eventClassType" minOccurs="0"/>
          <xs:element name="includedInClass0Response" type="includedInClass0ResponseType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Define base type for all output data points -->
  <xs:complexType name="outputPointType">
    <xs:complexContent>
      <xs:extension base="pointType">
        <xs:sequence>
          <xs:element name="changeEventClass" type="eventClassType" minOccurs="0"/>
          <xs:element name="commandEventClass" type="eventClassType" minOccurs="0"/>
          <xs:element name="includedInClass0Response" type="includedInClass0ResponseType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Define base type for all string data points -->
  <xs:complexType name="stringPointType">
    <xs:complexContent>
      <xs:extension base="pointType">
        <xs:sequence>
          <xs:element name="changeEventClass" type="eventClassType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Define base type for all object groups -->
  <xs:complexType name="objectGroupType">
    <xs:sequence>
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Define base type for all input object groups -->
  <xs:complexType name="inputGroupType">
    <xs:complexContent>
      <xs:extension base="objectGroupType"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- Define base type for all output object groups -->
  <xs:complexType name="outputGroupType">
    <xs:complexContent>
      <xs:extension base="objectGroupType"/>
    </xs:complexContent>
  </xs:complexType>
  <!-- *************************** -->
  <!-- 3.1) Binary Input Points    -->
  <!-- *************************** -->
  <!-- 3.1.1) Static Variation Reported When Variation 0 Requested -->
  <xs:complexType name="binaryInputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryInputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultBinaryInputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="binaryInputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="binaryInputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.1.2) Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="binaryInputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryInputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultBinaryInputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="binaryInputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="binaryInputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.1.3) Event Reporting Mode -->
  <xs:complexType name="eventReportingModeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventReportingModeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventReportingModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="eventReportingModeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="eventReportingModeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.1.4) Binary Inputs Included In Class 0 Response -->
  <xs:complexType name="class0ResponseModeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="onlyWhenAssignedToClass123" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="class0ResponseModeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="onlyWhenAssignedToClass123" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="class0ResponseModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="class0ResponseModeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="class0ResponseModeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="eventBufferPerObjectGroupCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventBufferPerObjectGroupCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="numEvents" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="eventBufferPerObjectGroupType">
    <xs:sequence>
      <xs:element name="capabilities" type="eventBufferPerObjectGroupCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="eventBufferPerObjectGroupCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Binary Input Group Type -->
  <xs:complexType name="binaryInputGroupType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.1.1) Static Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultStaticVariation" type="defaultBinaryInputStaticVariationType" minOccurs="0"/>
                <!-- 3.1.2) Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultEventVariation" type="defaultBinaryInputEventVariationType" minOccurs="0"/>
                <!-- 3.1.3) Event Reporting Mode -->
                <xs:element name="eventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.1.4) Binary Inputs Included In Class 0 Response -->
                <xs:element name="class0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.1.5) Binary Inputs Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ******************************* -->
  <!-- 3.2) Double Bit Input Points    -->
  <!-- ******************************* -->
  <!-- 3.2.1) Static Variation Reported When Variation 0 Requested -->
  <xs:complexType name="doubleBitInputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="doubleBitInputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultDoubleBitInputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="doubleBitInputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="doubleBitInputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.2.2) Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="doubleBitInputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="doubleBitInputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultDoubleBitInputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="doubleBitInputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="doubleBitInputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.2.3) Event Reporting Mode (See parameter 3.1.3) -->
  <!-- 3.2.4) Double Bit Inputs Included In Class 0 Response (See parameter 3.1.4) -->
  <!-- Double Bit Input Point Type -->
  <!-- Double Bit Input Group Type -->
  <xs:complexType name="doubleBitInputGroupType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.2.1) Static Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultStaticVariation" type="defaultDoubleBitInputStaticVariationType" minOccurs="0"/>
                <!-- 3.2.2) Change Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultEventVariation" type="defaultDoubleBitInputEventVariationType" minOccurs="0"/>
                <!-- 3.2.3) Event Reporting Mode -->
                <xs:element name="eventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.2.4) Double Bit Inputs Included In Class 0 Response -->
                <xs:element name="class0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.2.5) Double-bit Inputs Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ******************************************************** -->
  <!-- 3.3) Binary Output Status and Control Relay Output Block -->
  <!-- ******************************************************** -->
  <!-- 3.3.1) Minimum Pulse Time Allowed with Trip, Close, and Pulse On Commands -->
  <xs:complexType name="pulseTimeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:double" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="pulseTimeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="fixed" type="xs:double" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="minimumPulseTimeType">
    <xs:sequence>
      <xs:element name="capabilities" type="pulseTimeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="pulseTimeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.2) Maximum Pulse Time Allowed with Trip, Close, and Pulse On Commands -->
  <xs:complexType name="maximumPulseTimeType">
    <xs:sequence>
      <xs:element name="capabilities" type="pulseTimeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="pulseTimeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.3) Binary Output Status Included In Class 0 Response (See parameter 3.1.4) -->
  <!-- 3.3.4) Reports Output Command Event Objects -->
  <xs:complexType name="outputCommandEventObjectsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="onSuccess" type="emptyElement" minOccurs="0"/>
          <xs:element name="allControlAttempts" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="outputCommandEventObjectsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="onSuccess" type="emptyElement" minOccurs="0"/>
          <xs:element name="allControlAttempts" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="outputCommandEventObjectsType">
    <xs:sequence>
      <xs:element name="capabilities" type="outputCommandEventObjectsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="outputCommandEventObjectsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.5) Static Variation Reported When Variation 0 Requested -->
  <xs:complexType name="binaryOutputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryOutputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryOutputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="binaryOutputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="binaryOutputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.6) Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="binaryOutputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryOutputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryOutputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="binaryOutputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="binaryOutputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.7) Command Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="binaryOutputCommandEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="binaryOutputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="binaryOutputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.3.8) Event Reporting Mode (See parameter 3.1.3) -->
  <!-- 3.3.9) Command Event Reporting Mode (See parameter 3.1.3) -->
  <!-- 3.3.10) Maximum Time Between Select and Operate -->
  <xs:complexType name="maxTimeBetweenSelectAndOperateCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTimeBetweenSelectAndOperateCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="notApplicable" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="variable" type="customType" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxTimeBetweenSelectAndOperateType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxTimeBetweenSelectAndOperateCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxTimeBetweenSelectAndOperateCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Define the binary output group type -->
  <xs:complexType name="binaryOutputGroupType">
    <xs:complexContent>
      <xs:extension base="outputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.3.1) Minimum Pulse Time Allowed With Trip, Close, and Pulse On Commands -->
                <xs:element name="minimumPulseTime" type="minimumPulseTimeType" minOccurs="0"/>
                <!-- 3.3.2) Maximum Pulse Time Allowed With Trip, Close, and Pulse On Commands -->
                <xs:element name="maximumPulseTime" type="maximumPulseTimeType" minOccurs="0"/>
                <!-- 3.3.3) Binary Output Status Included In Class 0 Response -->
                <xs:element name="class0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.3.4) Reports Output Command Event Objects -->
                <xs:element name="outputCommandEventObjects" type="outputCommandEventObjectsType" minOccurs="0"/>
                <!-- 3.3.5) Static Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultStaticVariation" type="binaryOutputStaticVariationType" minOccurs="0"/>
                <!-- 3.3.6) Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultEventVariation" type="binaryOutputEventVariationType" minOccurs="0"/>
                <!-- 3.3.7) Command Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultCommandEventVariation" type="binaryOutputCommandEventVariationType" minOccurs="0"/>
                <!-- 3.3.8) Change Event Reporting Mode -->
                <xs:element name="changeEventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.3.9) Command Event Reporting Mode -->
                <xs:element name="commandEventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.3.10) Maximum Time Between Select and Operate -->
                <xs:element name="maxTimeBetweenSelectAndOperate" type="maxTimeBetweenSelectAndOperateType" minOccurs="0"/>
                <!-- 3.3.11) Binary Outputs Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.3.12) Binary Output Commands Event Buffer Organization -->
                <xs:element name="commandEventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ***************************** -->
  <!-- 3.4) Counters/Frozen Counters -->
  <!-- ***************************** -->
  <!-- 3.4.1) Static Counter Variation Reported When Variation 0 Requested -->
  <xs:complexType name="counterStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultCounterStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="counterStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="counterStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.2) Counter Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="counterEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultCounterEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="counterEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="counterEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.3) Counters Included In Class 0 Response (see parameter 3.1.4) -->
  <!-- 3.4.4) Counter Event Reporting Mode  (see 3.5.3)-->
  <!-- 3.4.5) Static Frozen Counter Variation Reported When Variation 0 Requested -->
  <xs:complexType name="frozenCounterStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="nine" type="emptyElement" minOccurs="0"/>
          <xs:element name="ten" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="nine" type="emptyElement" minOccurs="0"/>
          <xs:element name="ten" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultFrozenCounterStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenCounterStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenCounterStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.6) Frozen Counter Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="frozenCounterEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultFrozenCounterEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenCounterEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenCounterEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.7) Frozen Counters Included In Class 0 Response (see parameter 3.1.4) -->
  <!-- 3.4.8) Frozen Counter Event Reporting Mode  -->
  <xs:complexType name="frozenCounterEventReportingModeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterEventReportingModeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterEventReportingModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenCounterEventReportingModeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenCounterEventReportingModeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.9) Counters Roll Over at -->
  <xs:complexType name="counterRollOverCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="sixteenBits" type="emptyElement" minOccurs="0"/>
          <xs:element name="thirtyTwoBits" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixed" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="range" type="positiveIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:positiveInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterRollOverCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="sixteenBits" type="emptyElement" minOccurs="0"/>
          <xs:element name="thirtyTwoBits" type="emptyElement" minOccurs="0"/>
          <xs:element name="value" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterRollOverType">
    <xs:sequence>
      <xs:element name="capabilities" type="counterRollOverCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="counterRollOverCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.10) Counters frozen by means of -->
  <xs:complexType name="countersFrozenCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="masterRequest" type="emptyElement" minOccurs="0"/>
          <xs:element name="localFreezeWithoutTimeOfDay" type="emptyElement" minOccurs="0"/>
          <xs:element name="localFreezeRequiredTimeOfDay" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="countersFrozenCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="masterRequest" type="emptyElement" minOccurs="0"/>
          <xs:element name="localFreezeWithoutTimeOfDay" type="emptyElement" minOccurs="0"/>
          <xs:element name="localFreezeRequiredTimeOfDay" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="countersFrozenType">
    <xs:sequence>
      <xs:element name="capabilities" type="countersFrozenCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="countersFrozenCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.4.13) Reports counter events for change of value -->
  <xs:complexType name="reportValueChangeCounterEventsTypeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="reportValueChangeCounterEventsTypeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="no" type="emptyElement" minOccurs="0"/>
          <xs:element name="yes" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="reportValueChangeCounterEventsType">
    <xs:sequence>
      <xs:element name="capabilities" type="reportValueChangeCounterEventsTypeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="reportValueChangeCounterEventsTypeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Define the counter group type -->
  <xs:complexType name="counterGroupType">
    <xs:complexContent>
      <xs:extension base="objectGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.4.1) Static Counter Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultCounterStaticVariation" type="defaultCounterStaticVariationType" minOccurs="0"/>
                <!-- 3.4.2) Counter Change Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultCounterEventVariation" type="defaultCounterEventVariationType" minOccurs="0"/>
                <!-- 3.4.2) Counters Included In Class 0 Response -->
                <xs:element name="counterClass0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.4.4) Counter Change Event Reporting Mode -->
                <xs:element name="counterEventReportingMode" type="analogEventReportingModeType" minOccurs="0"/>
                <!-- 3.4.5) Static Frozen Counter Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultFrozenCounterStaticVariation" type="defaultFrozenCounterStaticVariationType" minOccurs="0"/>
                <!-- 3.4.6) Frozen Counter Change Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultFrozenCounterEventVariation" type="defaultFrozenCounterEventVariationType" minOccurs="0"/>
                <!-- 3.4.7) Frozen Counters Included In Class 0 Response -->
                <xs:element name="frozenCounterClass0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.4.8) Frozen Counter Event Reporting Mode -->
                <xs:element name="frozenCounterEventReportingMode" type="frozenCounterEventReportingModeType" minOccurs="0"/>
                <!-- 3.4.9) Counters Roll Over At -->
                <xs:element name="counterRollOver" type="counterRollOverType" minOccurs="0"/>
                <!-- 3.4.10) Counters Frozen By Means Of -->
                <xs:element name="countersFrozen" type="countersFrozenType" minOccurs="0"/>
                <!-- 3.4.11) Counters Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.4.12) Frozen Counters Event Buffer Organization -->
                <xs:element name="frozenEventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.4.13) Reports counter events for change of value: -->
                <xs:element name="reportValueChangeCounterEvents" type="reportValueChangeCounterEventsType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ************************ -->
  <!-- 3.5) Analog Input Points -->
  <!-- ************************ -->
  <!-- 3.5.1) Static Variation Reported When Variation 0 Requested -->
  <xs:complexType name="analogInputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultAnalogInputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogInputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogInputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.2) Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="analogInputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultAnalogInputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogInputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogInputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.3) Event Reporting Mode  -->
  <xs:complexType name="analogEventReportingModeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="mostRecentEventTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="mostRecentResponseTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogEventReportingModeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="mostRecentEventTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="mostRecentResponseTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogEventReportingModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogEventReportingModeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogEventReportingModeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.4) Analog Inputs Included In Class 0 Response (See parameter 3.1.4) -->
  <!-- 3.5.5) Analog Deadband Assignments -->
  <xs:complexType name="analogDeadbandAssignmentsCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableViaDNP3" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableViaOtherMeans" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAssignmentsCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableViaDNP3" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableViaOtherMeans" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAssignmentsType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogDeadbandAssignmentsCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogDeadbandAssignmentsCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.6) Analog Deadband Algorithm -->
  <xs:complexType name="analogDeadbandAlgorithmCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="simple" type="emptyElement" minOccurs="0"/>
          <xs:element name="integrating" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAlgorithmCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="simple" type="emptyElement" minOccurs="0"/>
          <xs:element name="integrating" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAlgorithmType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogDeadbandAlgorithmCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogDeadbandAlgorithmCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.7) Static Frozen Analog Input Variation Reported When Variation 0 Requested -->
  <xs:complexType name="frozenAnalogInputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenAnalogInputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultFrozenAnalogInputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenAnalogInputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenAnalogInputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.8) Frozen Analog Input event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="frozenAnalogInputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenAnalogInputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultFrozenAnalogInputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenAnalogInputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenAnalogInputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.5.10) Frozen Analog Input Event Reporting Mode  -->
  <xs:complexType name="frozenAnalogEventReportingModeCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenAnalogEventReportingModeCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="mostRecent" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenAnalogEventReportingModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="frozenAnalogEventReportingModeCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="frozenAnalogEventReportingModeCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Analog Input Group Type -->
  <xs:complexType name="analogInputGroupType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.5.1) Static Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultStaticVariation" type="defaultAnalogInputStaticVariationType" minOccurs="0"/>
                <!-- 3.5.2) Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultEventVariation" type="defaultAnalogInputEventVariationType" minOccurs="0"/>
                <!-- 3.5.3) Event Reporting Mode -->
                <xs:element name="analogEventReportingMode" type="analogEventReportingModeType" minOccurs="0"/>
                <!-- 3.5.4) Analog Inputs Included In Class 0 Response -->
                <xs:element name="analogInputClass0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.5.5) Analog Deadband Assignments -->
                <xs:element name="analogDeadbandAssignments" type="analogDeadbandAssignmentsType" minOccurs="0"/>
                <!-- 3.5.6) Analog Deadband Algorithm -->
                <xs:element name="analogDeadbandAlgorithm" type="analogDeadbandAlgorithmType" minOccurs="0"/>
                <!-- 3.5.7) Static Frozen Analog Input Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultFrozenAnalogInputStaticVariation" type="defaultFrozenAnalogInputStaticVariationType" minOccurs="0"/>
                <!-- 3.5.8) Frozen Analog Input Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultFrozenAnalogInputEventVariation" type="defaultFrozenAnalogInputEventVariationType" minOccurs="0"/>
                <!-- 3.5.9) Frozen Analog Inputs Included In Class 0 Response -->
                <xs:element name="frozenAnalogInputClass0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.5.10) Frozen Analog Input Event Reporting Mode -->
                <xs:element name="frozenAnalogEventReportingMode" type="frozenAnalogEventReportingModeType" minOccurs="0"/>
                <!-- 3.5.11) Analog Inputs Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.5.12) Frozen Analog Inputs Event Buffer Organization -->
                <xs:element name="frozenEventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ************************* -->
  <!-- 3.6) Analog Output Points -->
  <!-- ************************* -->
  <!-- 3.6.1) Static Variation Reported When Variation 0 Requested -->
  <xs:complexType name="analogOutputStaticVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogOutputStaticVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="defaultAnalogOutputStaticVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogOutputStaticVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogOutputStaticVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.6.2) Analog Output Status Included In Class 0 Response (See parameter 3.1.4) -->
  <!-- 3.6.3) Reports Output Command Event Objects (See parameter 3.3.4) -->
  <!-- 3.6.4) Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="analogOutputEventVariationCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogOutputEventVariationCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogOutputEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogOutputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogOutputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.6.5) Command Event Variation Reported When Variation 0 Requested -->
  <xs:complexType name="analogOutputCommandEventVariationType">
    <xs:sequence>
      <xs:element name="capabilities" type="analogOutputEventVariationCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="analogOutputEventVariationCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.6.6) Event Reporting Mode (See parameter 3.1.3) -->
  <!-- 3.6.7) Command Event Reporting Mode (See parameter 3.1.3) -->
  <!-- 3.6.8) Maximum Time Between Select and Operate (See parameter 3.3.9) -->
  <!-- Analog Output Group -->
  <xs:complexType name="analogOutputGroupType">
    <xs:complexContent>
      <xs:extension base="outputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.6.1) Static Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultStaticVariation" type="defaultAnalogOutputStaticVariationType" minOccurs="0"/>
                <!-- 3.6.2) Analog Outputs Included In Class 0 Response -->
                <xs:element name="class0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.6.3) Reports Output Command Event Objects -->
                <xs:element name="outputCommandEventObjects" type="outputCommandEventObjectsType" minOccurs="0"/>
                <!-- 3.6.4) Change Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultChangeEventVariation" type="analogOutputEventVariationType" minOccurs="0"/>
                <!-- 3.6.5) Command Event Variation Reported When Variation 0 Requested -->
                <xs:element name="defaultCommandEventVariation" type="analogOutputCommandEventVariationType" minOccurs="0"/>
                <!-- 3.6.6) Change Event Reporting Mode -->
                <xs:element name="changeEventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.6.7) Command Event Reporting Mode -->
                <xs:element name="commandEventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.6.8) Maximum Time Between Select and Operate -->
                <xs:element name="maxTimeBetweenSelectAndOperate" type="maxTimeBetweenSelectAndOperateType" minOccurs="0"/>
                <!-- 3.6.9) Analog Outputs Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.6.10) Analog Output Commands Event Buffer Organization -->
                <xs:element name="commandEventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ************************************* -->
  <!-- 3.7) Sequential File Transfer Support -->
  <!-- ************************************* -->
  <!-- 3.7.1) File Transfer Supported -->
  <xs:complexType name="fileTransferSupportedType">
    <xs:sequence>
      <xs:element name="capabilities" type="yesNoCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.7.2) File Authentication -->
  <xs:complexType name="sometimesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="sometimesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="sometimes" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="fileAuthenticationType">
    <xs:sequence>
      <xs:element name="capabilities" type="sometimesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sometimesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.7.3) File Append Mode -->
  <xs:complexType name="fileAppendModeType">
    <xs:sequence>
      <xs:element name="capabilities" type="sometimesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="sometimesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.7.4) Permissions Support -->
  <xs:complexType name="permissionsSupportCapabilitiesType">
    <xs:sequence>
      <xs:element name="ownerReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="ownerWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="ownerExecuteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupExecuteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldExecuteAllowed" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="permissionsSupportCurrentValueType">
    <xs:sequence>
      <xs:element name="ownerReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="ownerWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="ownerExecuteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="groupExecuteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldReadAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldWriteAllowed" type="emptyElement" minOccurs="0"/>
      <xs:element name="worldExecuteAllowed" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="permissionsSupportType">
    <xs:sequence>
      <xs:element name="capabilities" type="permissionsSupportCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="permissionsSupportCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.7.5) Multiple Blocks in a Fragment -->
  <xs:complexType name="multipleBlocksInFragmentType">
    <xs:sequence>
      <xs:element name="capabilities" type="yesNoCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="yesNoCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3.7.6) Max Number of Files Open at One Time -->
  <xs:complexType name="maxOpenFilesCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="range" type="nonNegativeIntRangeType" minOccurs="0"/>
          <xs:element name="selectable" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="configurableOther" type="configurableCustomType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxOpenFilesCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="value" type="xs:nonNegativeInteger" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="maxOpenFilesType">
    <xs:sequence>
      <xs:element name="capabilities" type="maxOpenFilesCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="maxOpenFilesCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="sequentialFileTransferType">
    <xs:sequence>
      <xs:element name="configuration" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <!-- 3.7.1) File Transfer Supported -->
            <xs:element name="fileTransferSupported" type="fileTransferSupportedType" minOccurs="0"/>
            <!-- 3.7.2) File Authentication -->
            <xs:element name="fileAuthentication" type="fileAuthenticationType" minOccurs="0"/>
            <!-- 3.7.3) File Append Mode -->
            <xs:element name="fileAppendMode" type="fileAppendModeType" minOccurs="0"/>
            <!-- 3.7.4) Permissions Support -->
            <xs:element name="permissionsSupport" type="permissionsSupportType" minOccurs="0"/>
            <!-- 3.7.5) Multiple Blocks in a Fragment -->
            <xs:element name="multipleBlocksInFragment" type="multipleBlocksInFragmentType" minOccurs="0"/>
            <!-- 3.7.6) Max Number of Files Open at One Time -->
            <xs:element name="maxOpenFiles" type="maxOpenFilesType" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- ************************ -->
  <!-- 3.8) Octet String Points -->
  <!-- ************************ -->
  <!-- Octet String Group -->
  <xs:complexType name="objectGroupSelectionCapabilitiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="fixedGroup110" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedGroup114" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableEither" type="emptyElement" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="configurableEitherType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="g110"/>
      <xs:enumeration value="g114"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="objectGroupSelectionCurrentValueType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="fixedGroup110" type="emptyElement" minOccurs="0"/>
          <xs:element name="fixedGroup114" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableEither" type="configurableEitherType" minOccurs="0"/>
          <xs:element name="basedOnPointIndex" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="objectGroupSelectionType">
    <xs:sequence>
      <xs:element name="capabilities" type="objectGroupSelectionCapabilitiesType" minOccurs="0"/>
      <xs:element name="currentValue" type="objectGroupSelectionCurrentValueType" minOccurs="0"/>
      <xs:element name="methods" type="configurationMethodsDataType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="octetStringGroupType">
    <xs:complexContent>
      <xs:extension base="objectGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.8.1) Event Reporting Mode -->
                <xs:element name="eventReportingMode" type="eventReportingModeType" minOccurs="0"/>
                <!-- 3.8.2) Octet Strings Included In Class 0 Response -->
                <xs:element name="class0ResponseMode" type="class0ResponseModeType" minOccurs="0"/>
                <!-- 3.8.3) Octet Strings Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
                <!-- 3.8.4) Object Group Selection -->
                <xs:element name="objectGroupSelection" type="objectGroupSelectionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Octet String Points -->
                <xs:element name="octetString" type="octetStringPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ****************************** -->
  <!-- 3.9) Virtual Terminal Points -->
  <!-- ****************************** -->
  <!-- Virtual Terminal Group -->
  <xs:complexType name="virtualTerminalGroupType">
      <xs:complexContent>
      <xs:extension base="objectGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 3.9.1) Virtual Terminals Event Buffer Organization -->
                <xs:element name="eventBufferPerObjectGroup" type="eventBufferPerObjectGroupType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- ************************ -->
  <!-- 3.10) Dataset Prototypes -->
  <!-- ************************ -->
  <xs:complexType name="datasetPrototypeType">
  </xs:complexType>
  <!-- ************************ -->
  <!-- 3.11) Dataset Descriptor -->
  <!-- ************************ -->
  <!-- Dataset Descriptor Type -->
  <xs:complexType name="datasetDescriptorType">
  </xs:complexType>

  <!-- **************************** -->
  <!-- 4) DNP3 Implementation Table -->
  <!-- **************************** -->
  <!-- Implementation Table -->
  <xs:complexType name="reqRespSupportType">
    <xs:sequence>
      <xs:element name="functionCode" type="xs:positiveInteger"/>
      <xs:element name="qualifierCode" type="xs:nonNegativeInteger" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="supportedVariationType">
    <xs:sequence>
      <xs:element name="objectGroup" type="xs:nonNegativeInteger"/>
      <xs:element name="variation" type="xs:nonNegativeInteger"/>
      <xs:element name="description" type="xs:string" minOccurs="0"/>
      <xs:element name="request" type="reqRespSupportType" minOccurs="0"/>
      <xs:element name="response" type="reqRespSupportType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="reqFunctionCodeType">
    <xs:sequence>
      <xs:element name="functionCode" type="xs:nonNegativeInteger"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="supportedFunctionCodeType">
    <xs:sequence>
      <xs:element name="request" type="reqFunctionCodeType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="implementationTableType">
    <xs:sequence>
      <!-- Implementation Table -->
      <xs:element name="supportedVariation" type="supportedVariationType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="supportedFunctionCode" type="supportedFunctionCodeType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- *************************** -->
  <!-- 5.1) Binary Input Points    -->
  <!-- *************************** -->
  <!-- Definition of Binary Input Point List -->
  <xs:complexType name="pointListDefinitionType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurable" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryInputStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryInputEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryInputPointType">
    <xs:complexContent>
      <xs:extension base="inputPointType">
        <xs:sequence>
          <xs:element name="defaultStaticVariation" type="binaryInputStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultEventVariation" type="binaryInputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="nameState0" type="xs:string" minOccurs="0"/>
          <xs:element name="nameState1" type="xs:string" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="state" type="xs:boolean" minOccurs="0"/>
                <xs:element name="quality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Binary Input Points Type -->
  <xs:complexType name="binaryInputPointsType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.1) Definition Of Binary Input Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Binary Input Points -->
                <xs:element name="binaryInput" type="binaryInputPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- *************************** -->
  <!-- 5.2) Double-Bit Input Points-->
  <!-- *************************** -->
  <!-- Definition of Double Bit Input Point List -->
  <xs:complexType name="doubleBitInputStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="doubleBitInputEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="doubleBitInputPointType">
    <xs:complexContent>
      <xs:extension base="inputPointType">
        <xs:sequence>
          <xs:element name="defaultStaticVariation" type="doubleBitInputStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultEventVariation" type="doubleBitInputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="nameState0" type="xs:string" minOccurs="0"/>
          <xs:element name="nameState1" type="xs:string" minOccurs="0"/>
          <xs:element name="nameState2" type="xs:string" minOccurs="0"/>
          <xs:element name="nameState3" type="xs:string" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="state" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="quality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Double-Bit Input Points Type -->
  <xs:complexType name="doubleBitInputPointsType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.2) Definition Of Double-bit Input Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Double-bit Input Points -->
                <xs:element name="doubleBitInput" type="doubleBitInputPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- *************************** -->
  <!-- 5.3) Binary Output Points   -->
  <!-- *************************** -->
  <!-- Definition of Binary Output Point List -->
  <xs:complexType name="binaryOutputStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binaryOutputEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="binarySupportedControlOperationsType">
    <xs:sequence>
      <xs:element name="supportSelectOperate" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportDirectOperate" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportDirectOperateNoAck" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportPulseOn" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportPulseOff" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportLatchOn" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportLatchOff" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportTrip" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportClose" type="emptyElement" minOccurs="0"/>
      <xs:element name="countGreaterThanOne" type="emptyElement" minOccurs="0"/>
      <xs:element name="cancelCurrentOperation" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="binaryOutputPointType">
    <xs:complexContent>
      <xs:extension base="outputPointType">
        <xs:sequence>
          <xs:element name="defaultStaticVariation" type="binaryOutputStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultChangeEventVariation" type="binaryOutputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultCommandEventVariation" type="binaryOutputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="minimumPulseWidth" type="xs:double" minOccurs="0"/>
          <xs:element name="maximumPulseWidth" type="xs:double" minOccurs="0"/>
          <xs:element name="supportedControlOperations" type="binarySupportedControlOperationsType" minOccurs="0"/>
          <xs:element name="maxTimeSelectOperate" type="xs:double" minOccurs="0"/>
          <xs:element name="nameState0" type="xs:string" minOccurs="0"/>
          <xs:element name="nameState1" type="xs:string" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="state" type="xs:boolean" minOccurs="0"/>
                <xs:element name="quality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
                <xs:element name="control" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="status" type="controlStatusType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Binary Output Points Type -->
  <xs:complexType name="binaryOutputPointsType">
    <xs:complexContent>
      <xs:extension base="outputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.3) Definition Of Binary Output Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Binary Output Points -->
                <xs:element name="binaryOutput" type="binaryOutputPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- ********************* -->
  <!-- 5.4) Counter Points   -->
  <!-- ********************* -->
  <!-- Definition of Counter Point List -->
  <xs:complexType name="counterStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="nine" type="emptyElement" minOccurs="0"/>
          <xs:element name="ten" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="frozenCounterEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="counterPointType">
    <xs:complexContent>
      <xs:extension base="pointType">
        <xs:sequence>
          <xs:element name="defaultCounterStaticVariation" type="counterStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultCounterEventVariation" type="counterEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="countersIncludedInClass0" type="includedInClass0ResponseType" minOccurs="0"/>
          <xs:element name="counterEventClass" type="eventClassType" minOccurs="0"/>
          <xs:element name="frozenCounterExists" type="xs:boolean" minOccurs="0"/>
          <xs:element name="defaultFrozenCounterStaticVariation" type="frozenCounterStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultFrozenCounterEventVariation" type="frozenCounterEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="frozenCountersIncludedInClass0" type="includedInClass0ResponseType" minOccurs="0"/>
          <xs:element name="frozenCounterEventClass" type="eventClassType" minOccurs="0"/>
          <xs:element name="counterRollOver" type="xs:positiveInteger" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="runningCounterValue" type="xs:unsignedInt" minOccurs="0"/>
                <xs:element name="runningCounterQuality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="runningCounterTimestamp" type="xs:dateTime" minOccurs="0"/>
                <xs:element name="frozenCounterValue" type="xs:unsignedInt" minOccurs="0"/>
                <xs:element name="frozenCounterQuality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="frozenCounterTimestamp" type="xs:dateTime" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Counter Points Type -->
  <xs:complexType name="counterPointsType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.4) Definition Of Counter Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Counter Points -->
                <xs:element name="counter" type="counterPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- ************************** -->
  <!-- 5.5) Analog Input Points   -->
  <!-- ************************** -->
  <!-- Definition of Analog Input Point List -->
  <xs:complexType name="analogInputStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputEventReportingModePerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="mostRecentEventTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="mostRecentResponseTimeValue" type="emptyElement" minOccurs="0"/>
          <xs:element name="allEvents" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAssignmentsPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="configurableViaDNP3" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurableViaOtherMeans" type="customType" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogDeadbandAlgorithmPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="simple" type="emptyElement" minOccurs="0"/>
          <xs:element name="integrating" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputPointType">
    <xs:complexContent>
      <xs:extension base="inputPointType">
        <xs:sequence>
          <xs:element name="defaultStaticVariation" type="analogInputStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultEventVariation" type="analogInputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="analogEventReportingMode" type="analogInputEventReportingModePerPointType" minOccurs="0"/>
          <xs:element name="analogDeadbandAssignments" type="analogDeadbandAssignmentsPerPointType" minOccurs="0"/>
          <xs:element name="analogDeadbandAlgorithm" type="analogDeadbandAlgorithmPerPointType" minOccurs="0"/>
          <xs:element name="minIntegerTransmittedValue" type="xs:integer" minOccurs="0"/>
          <xs:element name="maxIntegerTransmittedValue" type="xs:integer" minOccurs="0"/>
          <xs:element name="minFloatTransmittedValue" type="xs:double" minOccurs="0"/>
          <xs:element name="maxFloatTransmittedValue" type="xs:double" minOccurs="0"/>
          <xs:element name="scaleOffset" type="xs:double" minOccurs="0"/>
          <xs:element name="scaleFactor" type="xs:double" minOccurs="0"/>
          <xs:element name="resolution" type="xs:double" minOccurs="0"/>
          <xs:element name="units" type="xs:string" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="value" type="xs:double" minOccurs="0"/>
                <xs:element name="quality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
                <xs:element name="deadband" type="xs:double" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogInputPointsType">
    <xs:complexContent>
      <xs:extension base="inputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.5) Definition Of Analog Input Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Analog Input Points -->
                <xs:element name="analogInput" type="analogInputPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- *************************** -->
  <!-- 5.6) Analog Output Points   -->
  <!-- *************************** -->
  <!-- Definition of Analog Output Point List -->
  <xs:complexType name="analogOutputStaticVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogOutputEventVariationPerPointType">
    <xs:complexContent>
      <xs:extension base="currentValueBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
          <xs:element name="four" type="emptyElement" minOccurs="0"/>
          <xs:element name="five" type="emptyElement" minOccurs="0"/>
          <xs:element name="six" type="emptyElement" minOccurs="0"/>
          <xs:element name="seven" type="emptyElement" minOccurs="0"/>
          <xs:element name="eight" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogSupportedControlOperationsType">
    <xs:sequence>
      <xs:element name="supportSelectOperate" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportDirectOperate" type="emptyElement" minOccurs="0"/>
      <xs:element name="supportDirectOperateNoAck" type="emptyElement" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="analogOutputPointType">
    <xs:complexContent>
      <xs:extension base="outputPointType">
        <xs:sequence>
          <xs:element name="defaultStaticVariation" type="analogOutputStaticVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultChangeEventVariation" type="analogOutputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="defaultCommandEventVariation" type="analogOutputEventVariationPerPointType" minOccurs="0"/>
          <xs:element name="maxTimeSelectOperate" type="xs:double" minOccurs="0"/>
          <xs:element name="supportedControlOperations" type="analogSupportedControlOperationsType" minOccurs="0"/>
          <xs:element name="minTransmittedValue" type="xs:double" minOccurs="0"/>
          <xs:element name="maxTransmittedValue" type="xs:double" minOccurs="0"/>
          <xs:element name="scaleOffset" type="xs:double" minOccurs="0"/>
          <xs:element name="scaleFactor" type="xs:double" minOccurs="0"/>
          <xs:element name="resolution" type="xs:double" minOccurs="0"/>
          <xs:element name="units" type="xs:string" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="value" type="xs:double" minOccurs="0"/>
                <xs:element name="quality" type="xs:unsignedByte" minOccurs="0"/>
                <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
                <xs:element name="control" type="xs:double" minOccurs="0"/>
                <xs:element name="status" type="controlStatusType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="analogOutputPointsType">
    <xs:complexContent>
      <xs:extension base="outputGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.6) Definition Of Analog Output Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Analog Output Points -->
                <xs:element name="analogOutput" type="analogOutputPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- *********************** -->
  <!-- 5.7) Sequential Files   -->
  <!-- *********************** -->
  <!-- File Definition -->
  <xs:complexType name="sequentialFileType">
    <xs:sequence>
      <xs:element name="fileName" type="xs:string" minOccurs="0"/>
      <xs:element name="eventClass" type="eventClassType" minOccurs="0"/>
      <xs:element name="readAuthenticateRequired" type="xs:boolean" minOccurs="0"/>
      <xs:element name="writeAuthenticateRequired" type="xs:boolean" minOccurs="0"/>
      <xs:element name="deleteAuthenticateRequired" type="xs:boolean" minOccurs="0"/>
      <xs:element name="description" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="sequentialFileListType">
    <xs:sequence>
      <xs:element name="configuration" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <!-- 5.7) Definition of File Names that may be read or written -->
            <xs:element name="fileListDefinition" type="pointListDefinitionType" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="files" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <!-- Sequential Files -->
            <xs:element name="sequentialFile" type="sequentialFileType" minOccurs="0" maxOccurs="unbounded"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <!-- *********************** -->
  <!-- 5.8) Octet String Points   -->
  <!-- *********************** -->
  <!-- String Points -->
  <xs:complexType name="groupNumberUsedType">
    <xs:choice>
      <xs:element name="group110" type="xs:string" minOccurs="0"/>
      <xs:element name="group114" type="xs:string" minOccurs="0"/>
    </xs:choice>
  </xs:complexType>
  <xs:complexType name="octetStringPointType">
    <xs:complexContent>
      <xs:extension base="stringPointType">
        <xs:sequence>
          <xs:element name="includedInClass0Response" type="includedInClass0ResponseType" minOccurs="0"/>
          <xs:element name="groupNumberUsed" type="groupNumberUsedType" minOccurs="0"/>
          <xs:element name="dnpData" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <xs:element name="value" type="xs:string" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="octetStringPointsType">
    <xs:complexContent>
      <xs:extension base="objectGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.8) Definition Of Octet String Point List -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Octet String Points -->
                <xs:element name="octetString" type="octetStringPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- ****************************** -->
  <!-- 5.9) Virtual Terminal Points -->
  <!-- ****************************** -->
  <!-- Virtual Terminal Points -->
  <xs:complexType name="virtualTerminalPointType">
    <xs:complexContent>
      <xs:extension base="stringPointType"/>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="virtualTerminalPointsType">
    <xs:complexContent>
      <xs:extension base="objectGroupType">
        <xs:sequence>
          <xs:element name="configuration" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- 5.9) Definition Of Virtual Terminal Port Numbers -->
                <xs:element name="pointListDefinition" type="pointListDefinitionType" minOccurs="0"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
          <xs:element name="dataPoints" minOccurs="0">
            <xs:complexType>
              <xs:sequence>
                <!-- Virtual Terminal Points -->
                <xs:element name="virtualTerminal" type="virtualTerminalPointType" minOccurs="0" maxOccurs="unbounded"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <!-- ************************ -->
  <!-- 5.10) Dataset Prototypes -->
  <!-- ************************ -->
  <!-- Data Set Prototypes -->
  <xs:complexType name="definitionOfDataSetType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="fixed" type="emptyElement" minOccurs="0"/>
          <xs:element name="configurable" type="emptyElement" minOccurs="0"/>
          <xs:element name="other" type="customType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Dataset Prototype Values -->
  <xs:simpleType name="datasetDataTypeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="vstr"/>
      <xs:enumeration value="uint"/>
      <xs:enumeration value="int"/>
      <xs:enumeration value="flt"/>
      <xs:enumeration value="ostr"/>
      <xs:enumeration value="bstr"/>
      <xs:enumeration value="time"/>
      <xs:enumeration value="uncd"/>
      <xs:enumeration value="none"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="datasetProtoElemTypeCodeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="id"/>
      <xs:enumeration value="uuid"/>
      <xs:enumeration value="nspc"/>
      <xs:enumeration value="name"/>
      <xs:enumeration value="dael"/>
      <xs:enumeration value="ctlv"/>
      <xs:enumeration value="ctls"/>
      <!-- a data set prototype may not include ptyp elements -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="datasetPrototypeListType">
    <xs:sequence>
      <xs:element name="configuration" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <!-- 5.10) Definition Of Dataset Prototypes -->
            <xs:element name="prototypeDefinition" type="definitionOfDataSetType" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="prototype" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <!-- Prototype ID -->
            <xs:element name="id" type="xs:unsignedInt"/>
            <!-- Prototype UUID -->
            <xs:element name="uuid" type="xs:string"/>
            <!-- Prototype Description -->
            <xs:element name="description" type="xs:string"/>
            <xs:sequence minOccurs="0">
              <!-- Prototype Namespace -->
              <xs:element name="namespace" type="xs:string"/>
              <!-- Prototype Name -->
              <xs:element name="name" type="xs:string"/>
            </xs:sequence>
            <!-- Prototype Data Elements -->
            <xs:element name="dataElement" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="elemTypeCode" type="datasetProtoElemTypeCodeEnum"/>
                  <xs:element name="dataType" type="datasetDataTypeEnum"/>
                  <xs:element name="maxDataLength" type="xs:unsignedByte"/>
                  <xs:element name="elementName" type="xs:string"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- ************************ -->
  <!-- 5.11) Dataset Descriptors -->
  <!-- ************************ -->
  <!-- Dataset Descriptor Element -->
  <xs:simpleType name="datasetDescrElemTypeCodeEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="id"/>
      <xs:enumeration value="name"/>
      <xs:enumeration value="dael"/>
      <xs:enumeration value="ptyp"/>
      <xs:enumeration value="ctlv"/>
      <xs:enumeration value="ctls"/>
      <!-- a data set descriptor may not include nspc or uuid elements -->
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="datasetDescriptorElementType">
    <xs:sequence>
      <xs:element name="elemTypeCode" type="datasetDescrElemTypeCodeEnum"/>
      <xs:element name="dataType" type="datasetDataTypeEnum"/>
      <xs:element name="maxDataLength" type="xs:unsignedByte"/>
      <xs:element name="elementName" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Dataset Prototype Element -->
  <xs:complexType name="datasetPrototypeElementType">
    <xs:sequence>
      <xs:element name="uuid" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="dataSetElementType">
    <xs:sequence>
      <xs:element name="description" type="xs:string" minOccurs="0"/>
      <xs:choice>
        <xs:element name="dataElement" type="datasetDescriptorElementType" minOccurs="0"/>
        <xs:element name="prototypeElement" type="datasetPrototypeElementType" minOccurs="0"/>
      </xs:choice>
    </xs:sequence>
  </xs:complexType>
  <!-- 5.11.1) Dataset Properties -->
  <xs:complexType name="datasetPropertiesType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:sequence>
          <xs:element name="readable" type="emptyElement" minOccurs="0"/>
          <xs:element name="writeable" type="emptyElement" minOccurs="0"/>
          <xs:element name="outstationMaintainsStaticData" type="emptyElement" minOccurs="0"/>
          <xs:element name="outstationGeneratesEventData" type="emptyElement" minOccurs="0"/>
          <xs:element name="datasetDefinedByMaster" type="emptyElement" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- 5.11.2) Default Event Assigned Class -->
  <xs:complexType name="eventAssignedClassType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="one" type="emptyElement" minOccurs="0"/>
          <xs:element name="two" type="emptyElement" minOccurs="0"/>
          <xs:element name="three" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- 5.11.3) Static data set included in class 0 response -->
  <xs:complexType name="datasetClass0ResponseModeType">
    <xs:complexContent>
      <xs:extension base="capabilitiesBaseType">
        <xs:choice>
          <xs:element name="always" type="emptyElement" minOccurs="0"/>
          <xs:element name="never" type="emptyElement" minOccurs="0"/>
          <xs:element name="onlyWhenAssignedToClass123" type="emptyElement" minOccurs="0"/>
        </xs:choice>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <!-- Dataset Descriptor Type -->
  <xs:complexType name="datasetDescriptorListType">
    <xs:sequence>
      <xs:element name="configuration" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <!-- 5.11) Data Set Descriptors -->
            <xs:element name="descriptorDefinition" type="definitionOfDataSetType" minOccurs="0"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="descriptor" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <!-- Dataset ID -->
            <xs:element name="id" type="xs:unsignedInt"/>
            <xs:element name="description" type="xs:string"/>
            <!-- 5.11.1) Dataset Properties -->
            <xs:element name="properties" type="datasetPropertiesType" minOccurs="0"/>
            <!-- 5.11.2)  Change Event Assigned Class -->
            <xs:element name="changeEventClass" type="eventAssignedClassType" minOccurs="0"/>
            <!-- 5.11.3) Static Data Set Included In Class 0 Response -->
            <xs:element name="class0ResponseMode" type="datasetClass0ResponseModeType" minOccurs="0"/>
            <xs:element name="datasetElements" minOccurs="0">
              <xs:complexType>
                <xs:sequence>
                  <!-- Dataset Name -->
                  <xs:element name="name" type="xs:string" minOccurs="0"/>
                  <!-- Dataset Elements -->
                  <xs:element name="dataSetElement" type="dataSetElementType" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
            <xs:element name="pointIndexAttribute" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:sequence>
                  <xs:element name="groupNumber" type="xs:nonNegativeInteger"/>
                  <xs:element name="pointIndex" type="xs:nonNegativeInteger"/>
                </xs:sequence>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="datasetPoint" minOccurs="0">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="dnpData" minOccurs="0" maxOccurs="unbounded">
              <xs:complexType>
                <xs:choice>
                  <xs:element name="vstr" type="xs:string" minOccurs="0"/>
                  <xs:element name="uint" type="xs:unsignedInt" minOccurs="0"/>
                  <xs:element name="int" type="xs:int" minOccurs="0"/>
                  <xs:element name="flt" type="xs:float" minOccurs="0"/>
                  <xs:element name="ostr" type="xs:string" minOccurs="0"/>
                  <xs:element name="bstr" type="xs:unsignedInt" minOccurs="0"/>
                  <xs:element name="time" type="xs:dateTime" minOccurs="0"/>
                  <xs:element name="uncd" type="xs:string" minOccurs="0"/>
                </xs:choice>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 1) DNP3 Configuration Element -->
  <xs:complexType name="dnpConfigurationType">
    <xs:sequence>
      <!-- 1.1) Device Information -->
      <xs:element name="deviceConfig" type="deviceConfigType" minOccurs="0"/>
      <!-- 1.2) Serial Connection Capabilities -->
      <xs:element name="serialConfig" type="serialConfigType" minOccurs="0"/>
      <!-- 1.3) IP Networking Capabilities -->
      <xs:element name="networkConfig" type="networkConfigType" minOccurs="0"/>
      <!-- 1.4) Data Link Layer Capabilities -->
      <xs:element name="linkConfig" type="linkConfigType" minOccurs="0"/>
      <!-- 1.5) Application Layer Capabilities -->
      <xs:element name="applConfig" type="applConfigType" minOccurs="0"/>
      <!-- 1.6) Master Configuration -->
      <xs:element name="masterConfig" type="masterConfigType" minOccurs="0"/>
      <!-- 1.7) Outstation Configuration -->
      <xs:element name="outstationConfig" type="outstationConfigType" minOccurs="0"/>
      <!-- 1.8) Outstation Unsolicited Configuration -->
      <xs:element name="unsolicitedConfig" type="unsolicitedConfigType" minOccurs="0"/>
      <!-- 1.9) Outstation Unsolicited Response Trigger Conditions -->
      <xs:element name="unsolicitedResponseTriggerConditions" type="unsolicitedResponseTriggerConditionsType" minOccurs="0"/>
      <!-- 1.10) Outstation Performance -->
      <xs:element name="outstationPerformance" type="outstationPerformanceType" minOccurs="0"/>
      <!-- 1.11) Outstation Field Parameters -->
      <xs:element name="fieldConfig" type="fieldConfigType" minOccurs="0"/>
      <!-- 1.12) Security Parameters -->
      <xs:element name="securityConfig" type="securityConfigType" minOccurs="0"/>
      <!-- 1.13) Broadcast Functionality -->
      <xs:element name="broadcastConfig" type="broadcastConfigType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 3) DNP3 Database Element -->
  <xs:complexType name="dnpDatabaseType">
    <xs:sequence>
      <!-- 3.1) Binary Input Points -->
      <xs:element name="binaryInputGroup" type="binaryInputGroupType" minOccurs="0"/>
      <!-- 3.2) Double Bit Input Points -->
      <xs:element name="doubleBitInputGroup" type="doubleBitInputGroupType" minOccurs="0"/>
      <!-- 3.3) Binary Output Points -->
      <xs:element name="binaryOutputGroup" type="binaryOutputGroupType" minOccurs="0"/>
      <!-- 3.4) Counter/Frozen Counter Points -->
      <xs:element name="counterGroup" type="counterGroupType" minOccurs="0"/>
      <!-- 3.5) Analog Input Points -->
      <xs:element name="analogInputGroup" type="analogInputGroupType" minOccurs="0"/>
      <!-- 3.6) Analog Output Points -->
      <xs:element name="analogOutputGroup" type="analogOutputGroupType" minOccurs="0"/>
      <!-- 3.7) Sequential Filter Transfer -->
      <xs:element name="sequentialFileTransfer" type="sequentialFileTransferType" minOccurs="0"/>
      <!-- 3.8) Octet String Points -->
      <xs:element name="octetStringGroup" type="octetStringGroupType" minOccurs="0"/>
      <!-- 3.9) Virtual Terminal Points -->
      <xs:element name="virtualTerminalGroup" type="virtualTerminalGroupType" minOccurs="0"/>
      <!-- 3.10) Dataset Prototype Configuration -->
      <xs:element name="datasetPrototype" type="datasetPrototypeType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- 3.11) Dataset Descriptor -->
      <xs:element name="datasetDescriptor" type="datasetDescriptorType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 4) Implementation Table -->
  <xs:complexType name="dnpImplementationTableType">
    <xs:sequence>
      <!-- 4.1) Implementation Table -->
      <xs:element name="table" type="implementationTableType"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- Allow notes at the end of the table -->
      <xs:element name="note" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- 5) Data Points List -->
  <xs:complexType name="dnpDataPointsListType">
    <xs:sequence>
      <!-- 5.1) Binary Input Points -->
      <xs:element name="binaryInputPoints" type="binaryInputPointsType" minOccurs="0"/>
      <!-- 5.2) Double-Bit Input Points -->
      <xs:element name="doubleBitInputPoints" type="doubleBitInputPointsType" minOccurs="0"/>
      <!-- 5.3) Binary Output Points -->
      <xs:element name="binaryOutputPoints" type="binaryOutputPointsType" minOccurs="0"/>
      <!-- 5.4) Counter Points -->
      <xs:element name="counterPoints" type="counterPointsType" minOccurs="0"/>
      <!-- 5.5) Analog Input Points -->
      <xs:element name="analogInputPoints" type="analogInputPointsType" minOccurs="0"/>
      <!-- 5.6) Analog Output Points -->
      <xs:element name="analogOutputPoints" type="analogOutputPointsType" minOccurs="0"/>
      <!-- 5.7) Sequential Files -->
      <xs:element name="sequentialFiles" type="sequentialFileListType" minOccurs="0"/>
      <!-- 5.8) Octet Stringss -->
      <xs:element name="octetStringPoints" type="octetStringPointsType" minOccurs="0"/>
      <!-- 5.9) Virtual Terminal Points -->
      <xs:element name="virtualTerminalPoints" type="virtualTerminalPointsType" minOccurs="0"/>
      <!-- 5.10) Dataset Prototypes  -->
      <xs:element name="datasetPrototype" type="datasetPrototypeListType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- 5.11) Dataset Descriptors -->
      <xs:element name="datasetDescriptor" type="datasetDescriptorListType" minOccurs="0" maxOccurs="unbounded"/>
      <!-- 5.13) User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <!-- Define DNP3 Device Status -->
  <xs:complexType name="dnp3DeviceOptionalType">
    <xs:sequence>
      <!-- 1) DNP3 Configuration -->
      <xs:element name="configuration" type="dnpConfigurationType" minOccurs="0"/>
      <!-- 2) Mapping to IEC 61850 Models -->
      <xs:element name="iec61850DeviceMapping" type="iec61850DeviceMappingType" minOccurs="0"/>
      <!-- 3) Capabilities and Current Settings for Device Database -->
      <xs:element name="database" type="dnpDatabaseType" minOccurs="0"/>
      <!-- 4) Implementation Table -->
      <xs:element name="implementationTable" type="dnpImplementationTableType" minOccurs="0"/>
      <!-- 5) Data Points List -->
      <xs:element name="dataPointsList" type="dnpDataPointsListType" minOccurs="0"/>
      <!-- User Defined Private Data -->
      <xs:element name="userData" type="userDataType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="description" type="xs:string" use="optional"/>
  </xs:complexType>
  <!-- Define the top level DNP3 Device Profile Document -->
  <xs:element name="DNP3DeviceProfileDocument">
    <xs:complexType>
      <xs:sequence>
        <!-- Document header -->
        <xs:element name="documentHeader" type="documentHeaderType" minOccurs="0"/>
        <xs:element name="referenceDevice" type="dnp3DeviceOptionalType" minOccurs="0"/>
        <xs:element name="auxiliaryInfo" type="dnp3DeviceOptionalType" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="schemaVersion" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
