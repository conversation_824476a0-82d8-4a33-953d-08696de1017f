<UserControl x:Class="DNP3Editor.Views.DocumentHeaderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Document Information Section -->
            <GroupBox Grid.Row="0" Header="Document Information" Margin="0,0,0,20">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="Document Name:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding DocumentName, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="5,0,0,0" Padding="5"/>

                    <Label Grid.Row="1" Grid.Column="0" Content="Description:" VerticalAlignment="Top" Margin="0,10,0,0"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DocumentDescription, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="5,10,0,0" Padding="5" Height="80" TextWrapping="Wrap" AcceptsReturn="True" 
                             VerticalScrollBarVisibility="Auto"/>
                </Grid>
            </GroupBox>

            <!-- Revision History Section -->
            <GroupBox Grid.Row="1" Header="Revision History" Margin="0,0,0,20">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Add Revision Button -->
                    <Button Grid.Row="0" Content="Add Revision Entry" Command="{Binding AddRevisionHistoryEntryCommand}"
                            HorizontalAlignment="Left" Margin="0,0,0,10" Padding="10,5"/>

                    <!-- Revision History List -->
                    <DataGrid Grid.Row="1" ItemsSource="{Binding RevisionHistory}" AutoGenerateColumns="False"
                              CanUserAddRows="False" CanUserDeleteRows="True" CanUserReorderColumns="False"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column" MinHeight="200">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Version" Binding="{Binding Version}" Width="80"/>
                            <DataGridTemplateColumn Header="Date" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <DatePicker SelectedDate="{Binding Date, UpdateSourceTrigger=PropertyChanged}" 
                                                    BorderThickness="0" Background="Transparent"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="Time" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <CheckBox IsChecked="{Binding TimeSpecified, UpdateSourceTrigger=PropertyChanged}" 
                                                      VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBox Text="{Binding Time, StringFormat=HH:mm:ss, UpdateSourceTrigger=PropertyChanged}" 
                                                     IsEnabled="{Binding TimeSpecified}" Width="70" BorderThickness="0" 
                                                     Background="Transparent"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="Author" Binding="{Binding Author}" Width="150"/>
                            <DataGridTextColumn Header="Reason" Binding="{Binding Reason}" Width="*"/>
                            <DataGridTemplateColumn Header="Actions" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="Remove" 
                                                Command="{Binding DataContext.RemoveRevisionHistoryEntryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}" 
                                                Padding="5,2" FontSize="10"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </GroupBox>

            <!-- Save Button -->
            <Button Grid.Row="2" Content="Save Changes" Command="{Binding SaveChangesCommand}"
                    HorizontalAlignment="Left" Margin="0,0,0,20" Padding="15,8" FontWeight="Bold"/>
        </Grid>
    </ScrollViewer>
</UserControl>
