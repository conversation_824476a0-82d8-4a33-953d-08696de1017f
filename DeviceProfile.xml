<?xml version="1.0" encoding="utf-8"?>
<DNP3DeviceProfileDocument schemaVersion="2.10.00" xmlns="http://www.dnp3.org/DNP3/DeviceProfile/November2014">
  <documentHeader>
    <documentName>New DNP3 Device Profile</documentName>
    <documentDescription>Device profile created with DNP3 Editor</documentDescription>
    <revisionHistory version="1">
      <date>2025-06-20</date>
      <author>f<PERSON><PERSON><PERSON></author>
      <reason>Initial creation</reason>
    </revisionHistory>
  </documentHeader>
  <referenceDevice description="Reference Device">
    <configuration>
      <deviceConfig>
        <deviceFunction>
          <currentValue />
        </deviceFunction>
        <vendorName>
          <currentValue>
            <value>rrr</value>
          </currentValue>
        </vendorName>
        <deviceName>
          <currentValue>
            <value>New ttt</value>
          </currentValue>
        </deviceName>
        <hardwareVersion>
          <currentValue>
            <value>5</value>
          </currentValue>
        </hardwareVersion>
        <softwareVersion>
          <currentValue>
            <value>5</value>
          </currentValue>
        </softwareVersion>
      </deviceConfig>
      <serialConfig>
        <portName>
          <currentValue>
            <value>COM1</value>
          </currentValue>
        </portName>
        <serialParameters>
          <currentValue />
        </serialParameters>
        <baudRate>
          <currentValue>
            <value>9600</value>
          </currentValue>
        </baudRate>
        <flowControl>
          <currentValue />
        </flowControl>
        <linkStatusInterval>
          <currentValue>
            <value>1000</value>
          </currentValue>
        </linkStatusInterval>
        <supportsCollisionAvoidance>
          <currentValue />
        </supportsCollisionAvoidance>
      </serialConfig>
      <linkConfig>
        <dataLinkAddress>
          <currentValue />
        </dataLinkAddress>
        <selfAddressSupport>
          <currentValue />
        </selfAddressSupport>
        <sendsConfirmedUserDataFrames>
          <currentValue />
        </sendsConfirmedUserDataFrames>
        <linkLayerConfirmTimeout>
          <currentValue>
            <value>1000</value>
          </currentValue>
        </linkLayerConfirmTimeout>
        <maxDataLinkRetries>
          <currentValue>
            <value>3</value>
          </currentValue>
        </maxDataLinkRetries>
      </linkConfig>
      <applConfig>
        <maxTransmittedFragmentSize>
          <currentValue>
            <value>2048</value>
          </currentValue>
        </maxTransmittedFragmentSize>
        <maxReceivedFragmentSize>
          <currentValue>
            <value>2048</value>
          </currentValue>
        </maxReceivedFragmentSize>
        <fragmentTimeout>
          <currentValue>
            <value>5000</value>
          </currentValue>
        </fragmentTimeout>
        <supportsMixedObjectGroupsInControlRequest>
          <currentValue />
        </supportsMixedObjectGroupsInControlRequest>
      </applConfig>
    </configuration>
    <dataPointsList>
      <binaryInputPoints>
        <dataPoints>
          <binaryInput>
            <index>0</index>
            <name>BinaryInput_0</name>
            <description>New binary input point</description>
          </binaryInput>
        </dataPoints>
      </binaryInputPoints>
    </dataPointsList>
  </referenceDevice>
</DNP3DeviceProfileDocument>