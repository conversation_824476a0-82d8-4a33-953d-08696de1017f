<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- Custom styles for the DNP3 Editor -->
    <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,10,0,5"/>
        <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundAccentBrush}"/>
    </Style>
    
    <Style x:Key="FieldLabelStyle" TargetType="Label">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="MinWidth" Value="120"/>
    </Style>
    
    <Style x:Key="ValidationErrorStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="Red"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>
    
    <!-- Additional styles for better UI consistency -->
    <Style x:Key="GroupBoxHeaderStyle" TargetType="GroupBox">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
    </Style>
    
    <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="Padding" Value="8,4"/>
    </Style>
    
    <Style x:Key="ToolBarButtonStyle" TargetType="Button">
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="MinWidth" Value="32"/>
        <Setter Property="MinHeight" Value="32"/>
    </Style>
    
</ResourceDictionary>
