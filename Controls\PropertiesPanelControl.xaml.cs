using System.Windows.Controls;
using System.Windows.Input;
using DNP3Editor.ViewModels;

namespace DNP3Editor.Controls;

/// <summary>
/// Interaction logic for PropertiesPanelControl.xaml
/// </summary>
public partial class PropertiesPanelControl : System.Windows.Controls.UserControl
{
    public PropertiesPanelControl()
    {
        InitializeComponent();
    }

    private void TextBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (sender is TextBox textBox && textBox.Tag is PropertyItem propertyItem)
        {
            // Check if this is a "not configured" property that can be initialized
            if (propertyItem.IsReadOnly && propertyItem.Value.Contains("not configured"))
            {
                // Try to initialize the property
                if (DataContext is PropertiesPanelViewModel viewModel)
                {
                    viewModel.InitializeProperty(propertyItem);
                }
            }
        }
    }
}
