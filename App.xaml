<Application x:Class="DNP3Editor.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             xmlns:local="clr-namespace:DNP3Editor">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- ModernWPF Theme -->
                <ui:ThemeResources />
                <ui:XamlControlsResources />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Themes/DNP3EditorStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Global Converters -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <local:InverseBooleanToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>