using System;

namespace DNP3Editor.Events;

public class ConfigurationStructureChangedEventArgs : EventArgs
{
    public ConfigurationSectionType SectionType { get; }

    public ConfigurationStructureChangedEventArgs(ConfigurationSectionType sectionType)
    {
        SectionType = sectionType;
    }
}

public enum ConfigurationSectionType
{
    Serial,
    Network,
    Security,
    Master,
    Outstation,
    DataPoints
}
