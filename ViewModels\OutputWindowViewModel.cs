using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using DNP3Editor.Models;

namespace DNP3Editor.ViewModels;

public partial class OutputWindowViewModel : ObservableObject
{
    public ObservableCollection<OutputMessage> Messages { get; } = [];

    [ObservableProperty]
    private bool showInfo = true;

    [ObservableProperty]
    private bool showWarnings = true;

    [ObservableProperty]
    private bool showErrors = true;

    public void AddMessage(string message, MessageType type)
    {
        var outputMessage = new OutputMessage
        {
            Message = message,
            Type = type,
            Timestamp = DateTime.Now
        };

        // Add to UI thread
        System.Windows.Application.Current.Dispatcher.Invoke(() =>
        {
            Messages.Add(outputMessage);
            
            // Limit messages to prevent memory issues
            if (Messages.Count > 1000)
            {
                Messages.RemoveAt(0);
            }
        });
    }

    public void ClearMessages()
    {
        Messages.Clear();
    }

    [RelayCommand]
    private void Clear()
    {
        ClearMessages();
    }
}
