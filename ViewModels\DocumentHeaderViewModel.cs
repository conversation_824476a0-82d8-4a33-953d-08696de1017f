using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DNP3Editor.Generated;
using System.Collections.ObjectModel;

namespace DNP3Editor.ViewModels;

public partial class DocumentHeaderViewModel : ObservableObject
{
    private readonly DocumentHeaderType _documentHeader;

    [ObservableProperty]
    private string documentName = string.Empty;

    [ObservableProperty]
    private string documentDescription = string.Empty;

    public ObservableCollection<RevisionHistoryItemViewModel> RevisionHistory { get; } = new();

    public DocumentHeaderViewModel(DocumentHeaderType documentHeader)
    {
        _documentHeader = documentHeader;
        LoadFromModel();
    }

    private void LoadFromModel()
    {
        DocumentName = _documentHeader.DocumentName ?? string.Empty;
        DocumentDescription = _documentHeader.DocumentDescription ?? string.Empty;

        RevisionHistory.Clear();
        foreach (var revision in _documentHeader.RevisionHistory)
        {
            RevisionHistory.Add(new RevisionHistoryItemViewModel(revision));
        }
    }

    [RelayCommand]
    private void AddRevisionHistoryEntry()
    {
        var newRevision = new DocumentHeaderTypeRevisionHistory
        {
            Version = (RevisionHistory.Count + 1).ToString(),
            Date = DateTime.Today,
            Author = Environment.UserName,
            Reason = "New revision"
        };

        _documentHeader.RevisionHistory.Add(newRevision);
        RevisionHistory.Add(new RevisionHistoryItemViewModel(newRevision));
    }

    [RelayCommand]
    private void RemoveRevisionHistoryEntry(RevisionHistoryItemViewModel? item)
    {
        if (item != null)
        {
            _documentHeader.RevisionHistory.Remove(item.Model);
            RevisionHistory.Remove(item);
        }
    }

    [RelayCommand]
    private void SaveChanges()
    {
        _documentHeader.DocumentName = DocumentName;
        _documentHeader.DocumentDescription = DocumentDescription;

        // Save changes from revision history items
        foreach (var item in RevisionHistory)
        {
            item.SaveToModel();
        }
    }

    partial void OnDocumentNameChanged(string value)
    {
        _documentHeader.DocumentName = value;
    }

    partial void OnDocumentDescriptionChanged(string value)
    {
        _documentHeader.DocumentDescription = value;
    }
}

public partial class RevisionHistoryItemViewModel : ObservableObject
{
    public DocumentHeaderTypeRevisionHistory Model { get; }

    [ObservableProperty]
    private string version = string.Empty;

    [ObservableProperty]
    private DateTime date = DateTime.Today;

    [ObservableProperty]
    private DateTime time = DateTime.Now;

    [ObservableProperty]
    private bool timeSpecified = false;

    [ObservableProperty]
    private string author = string.Empty;

    [ObservableProperty]
    private string reason = string.Empty;

    public RevisionHistoryItemViewModel(DocumentHeaderTypeRevisionHistory model)
    {
        Model = model;
        LoadFromModel();
    }

    private void LoadFromModel()
    {
        Version = Model.Version ?? string.Empty;
        Date = Model.Date;
        Time = Model.Time;
        TimeSpecified = Model.TimeSpecified;
        Author = Model.Author ?? string.Empty;
        Reason = Model.Reason ?? string.Empty;
    }

    public void SaveToModel()
    {
        Model.Version = Version;
        Model.Date = Date;
        Model.Time = Time;
        Model.TimeSpecified = TimeSpecified;
        Model.Author = Author;
        Model.Reason = Reason;
    }

    partial void OnVersionChanged(string value)
    {
        Model.Version = value;
    }

    partial void OnDateChanged(DateTime value)
    {
        Model.Date = value;
    }

    partial void OnTimeChanged(DateTime value)
    {
        Model.Time = value;
    }

    partial void OnTimeSpecifiedChanged(bool value)
    {
        Model.TimeSpecified = value;
    }

    partial void OnAuthorChanged(string value)
    {
        Model.Author = value;
    }

    partial void OnReasonChanged(string value)
    {
        Model.Reason = value;
    }
}
