using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DNP3Editor.Generated;
using DNP3Editor.Events;

namespace DNP3Editor.ViewModels;

public partial class DeviceConfigurationViewModel : ObservableObject
{
    private readonly DnpConfigurationType _configuration;
    private readonly Dnp3DeviceOptionalType? _parentDevice;

    // Event to notify when configuration is saved
    public event EventHandler? ConfigurationSaved;

    // Event to notify when configuration structure changes (new sections added)
    public event EventHandler<ConfigurationStructureChangedEventArgs>? ConfigurationStructureChanged;

    [ObservableProperty]
    private string vendorName = string.Empty;

    [ObservableProperty]
    private string deviceName = string.Empty;

    [ObservableProperty]
    private string hardwareVersion = string.Empty;

    [ObservableProperty]
    private string softwareVersion = string.Empty;

    [ObservableProperty]
    private bool isMasterDevice;

    [ObservableProperty]
    private bool isOutstationDevice;

    public DeviceConfigurationViewModel(DnpConfigurationType configuration, Dnp3DeviceOptionalType? parentDevice = null)
    {
        _configuration = configuration;
        _parentDevice = parentDevice;
        LoadFromConfiguration();
    }

    [RelayCommand]
    private void SaveConfiguration()
    {
        SaveToConfiguration();

        // Notify that configuration has been saved
        ConfigurationSaved?.Invoke(this, EventArgs.Empty);
    }

    [RelayCommand]
    private void AddSerialConfiguration()
    {
        if (_configuration.SerialConfig == null)
        {
            _configuration.SerialConfig = new SerialConfigType
            {
                PortName = new PortNameType
                {
                    CurrentValue = new PortNameCurrentValueType { Value = "COM1" }
                },
                BaudRate = new BaudRateType
                {
                    CurrentValue = new BaudRateCurrentValueType { Value = "9600" }
                },
                SerialParameters = new SerialParametersType
                {
                    CurrentValue = new SerialParametersCurrentValueType
                    {
                        Asynchronous = new EmptyElement()
                    }
                },
                FlowControl = new FlowControlType
                {
                    CurrentValue = new FlowControlCurrentValueType { None = new EmptyElement() }
                },
                LinkStatusInterval = new LinkStatusIntervalType
                {
                    CurrentValue = new LinkStatusIntervalCurrentValueType { Value = "1000" }
                },
                SupportsCollisionAvoidance = new SupportsCollisionAvoidanceType
                {
                    CurrentValue = new SupportsCollisionAvoidanceCurrentValueType { No = new EmptyElement() }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.Serial));
        }
    }

    [RelayCommand]
    private void AddNetworkConfiguration()
    {
        if (_configuration.NetworkConfig == null)
        {
            _configuration.NetworkConfig = new NetworkConfigType
            {
                PortName = new IpPortNameType
                {
                    CurrentValue = new IpPortNameCurrentValueType { Value = "20000" }
                },
                IpAddress = new IpAddressType
                {
                    CurrentValue = new IpAddressCurrentValueType { Address = "*************" }
                },
                TcpListenPort = new TcpListenPortType
                {
                    CurrentValue = new TcpListenPortCurrentValueType { Value = "20000" }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.Network));
        }
    }

    [RelayCommand]
    private void AddLinkLayerConfiguration()
    {
        if (_configuration.LinkConfig == null)
        {
            _configuration.LinkConfig = new LinkConfigType
            {
                DataLinkAddress = new DataLinkAddressType
                {
                    CurrentValue = new DataLinkAddressCurrentValueType { Value = 1 }
                },
                LinkLayerConfirmTimeout = new LinkLayerConfirmTimeoutType
                {
                    CurrentValue = new LinkLayerConfirmTimeoutCurrentValueType { Value = "1000" }
                },
                MaxDataLinkRetries = new MaxDataLinkRetriesType
                {
                    CurrentValue = new MaxDataLinkRetriesCurrentValueType { Value = "3" }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.LinkLayer));
        }
    }

    [RelayCommand]
    private void AddApplicationLayerConfiguration()
    {
        if (_configuration.ApplConfig == null)
        {
            _configuration.ApplConfig = new ApplConfigType
            {
                MaxTransmittedFragmentSize = new MaxTransmittedFragmentSizeType
                {
                    CurrentValue = new MaxFragmentSizeCurrentValueType { Value = "2048" }
                },
                MaxReceivedFragmentSize = new MaxReceivedFragmentSizeType
                {
                    CurrentValue = new MaxFragmentSizeCurrentValueType { Value = "2048" }
                },
                FragmentTimeout = new FragmentTimeoutType
                {
                    CurrentValue = new FragmentTimeoutCurrentValueType { Value = "5000" }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.ApplicationLayer));
        }
    }

    [RelayCommand]
    private void AddMasterConfiguration()
    {
        if (_configuration.MasterConfig == null && IsMasterDevice)
        {
            _configuration.MasterConfig = new MasterConfigType
            {
                ResponseTimeout = new ResponseTimeoutType
                {
                    CurrentValue = new ResponseTimeoutCurrentValueType { Value = "5000" }
                },
                ApplicationLayerRetries = new ApplicationLayerRetriesType
                {
                    CurrentValue = new ApplicationLayerRetriesCurrentValueType { Value = "3" }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.Master));
        }
    }

    [RelayCommand]
    private void AddOutstationConfiguration()
    {
        if (_configuration.OutstationConfig == null && IsOutstationDevice)
        {
            _configuration.OutstationConfig = new OutstationConfigType
            {
                ApplicationLayerConfirmTimeout = new ApplicationLayerConfirmTimeoutType
                {
                    CurrentValue = new ApplicationLayerConfirmTimeoutCurrentValueType { Value = "5000" }
                },
                TimeSyncRequired = new TimeSyncRequiredType
                {
                    CurrentValue = new TimeSyncRequiredCurrentValueType { Never = new EmptyElement() }
                }
            };

            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.Outstation));
        }
    }

    [RelayCommand]
    private void AddDataPointsList()
    {
        if (_parentDevice != null && _parentDevice.DataPointsList == null)
        {
            _parentDevice.DataPointsList = new DnpDataPointsListType();
            ConfigurationStructureChanged?.Invoke(this, new ConfigurationStructureChangedEventArgs(ConfigurationSectionType.DataPoints));
        }
    }

    [RelayCommand]
    private void ResetConfiguration()
    {
        LoadFromConfiguration();
    }

    private void LoadFromConfiguration()
    {
        if (_configuration.DeviceConfig != null)
        {
            VendorName = _configuration.DeviceConfig.VendorName?.CurrentValue?.Value ?? "";
            DeviceName = _configuration.DeviceConfig.DeviceName?.CurrentValue?.Value ?? "";
            HardwareVersion = _configuration.DeviceConfig.HardwareVersion?.CurrentValue?.Value ?? "";
            SoftwareVersion = _configuration.DeviceConfig.SoftwareVersion?.CurrentValue?.Value ?? "";

            // Device function
            var deviceFunction = _configuration.DeviceConfig.DeviceFunction?.CurrentValue;
            IsMasterDevice = deviceFunction?.Master != null;
            IsOutstationDevice = deviceFunction?.Outstation != null;
        }
    }

    private void SaveToConfiguration()
    {
        if (_configuration.DeviceConfig == null)
        {
            _configuration.DeviceConfig = new DeviceConfigType();
        }

        // Update vendor name
        if (_configuration.DeviceConfig.VendorName == null)
        {
            _configuration.DeviceConfig.VendorName = new VendorNameType();
        }
        if (_configuration.DeviceConfig.VendorName.CurrentValue == null)
        {
            _configuration.DeviceConfig.VendorName.CurrentValue = new VendorNameCurrentValueType();
        }
        _configuration.DeviceConfig.VendorName.CurrentValue.Value = VendorName;

        // Update device name
        if (_configuration.DeviceConfig.DeviceName == null)
        {
            _configuration.DeviceConfig.DeviceName = new DeviceNameType();
        }
        if (_configuration.DeviceConfig.DeviceName.CurrentValue == null)
        {
            _configuration.DeviceConfig.DeviceName.CurrentValue = new DeviceNameCurrentValueType();
        }
        _configuration.DeviceConfig.DeviceName.CurrentValue.Value = DeviceName;

        // Update hardware version
        if (!string.IsNullOrEmpty(HardwareVersion))
        {
            if (_configuration.DeviceConfig.HardwareVersion == null)
            {
                _configuration.DeviceConfig.HardwareVersion = new HardwareVersionType();
            }
            if (_configuration.DeviceConfig.HardwareVersion.CurrentValue == null)
            {
                _configuration.DeviceConfig.HardwareVersion.CurrentValue = new HardwareVersionCurrentValueType();
            }
            _configuration.DeviceConfig.HardwareVersion.CurrentValue.Value = HardwareVersion;
        }

        // Update software version
        if (!string.IsNullOrEmpty(SoftwareVersion))
        {
            if (_configuration.DeviceConfig.SoftwareVersion == null)
            {
                _configuration.DeviceConfig.SoftwareVersion = new SoftwareVersionType();
            }
            if (_configuration.DeviceConfig.SoftwareVersion.CurrentValue == null)
            {
                _configuration.DeviceConfig.SoftwareVersion.CurrentValue = new SoftwareVersionCurrentValueType();
            }
            _configuration.DeviceConfig.SoftwareVersion.CurrentValue.Value = SoftwareVersion;
        }

        // Update device function
        if (_configuration.DeviceConfig.DeviceFunction == null)
        {
            _configuration.DeviceConfig.DeviceFunction = new DeviceFunctionType();
        }
        if (_configuration.DeviceConfig.DeviceFunction.CurrentValue == null)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue = new DeviceFunctionCurrentValueType();
        }

        if (IsMasterDevice)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Master = new EmptyElement();
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Outstation = null;
        }
        else if (IsOutstationDevice)
        {
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Outstation = new EmptyElement();
            _configuration.DeviceConfig.DeviceFunction.CurrentValue.Master = null;
        }
    }
}
