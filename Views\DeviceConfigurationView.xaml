<UserControl x:Class="DNP3Editor.Views.DeviceConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="10">
            <!-- Device Information Section -->
            <GroupBox Header="Device Information" Margin="0,0,0,10">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="Vendor Name:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding VendorName}" Margin="5"/>

                    <Label Grid.Row="1" Grid.Column="0" Content="Device Name:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding DeviceName}" Margin="5"/>

                    <Label Grid.Row="2" Grid.Column="0" Content="Hardware Version:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding HardwareVersion}" Margin="5"/>

                    <Label Grid.Row="3" Grid.Column="0" Content="Software Version:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding SoftwareVersion}" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- Device Function Section -->
            <GroupBox Header="Device Function" Margin="0,0,0,10">
                <StackPanel Margin="10">
                    <RadioButton Content="Master Device" IsChecked="{Binding IsMasterDevice}" Margin="0,5"/>
                    <RadioButton Content="Outstation Device" IsChecked="{Binding IsOutstationDevice}" Margin="0,5"/>
                </StackPanel>
            </GroupBox>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10">
                <Button Content="Save" Command="{Binding SaveConfigurationCommand}"
                        Style="{StaticResource AccentButtonStyle}" Margin="0,0,10,0"/>
                <Button Content="Reset" Command="{Binding ResetConfigurationCommand}" Margin="0,0,10,0"/>
            </StackPanel>

            <!-- Add Configuration Sections -->
            <GroupBox Header="Add Configuration Sections" Margin="0,10">
                <StackPanel>
                    <TextBlock Text="Add additional configuration sections to your device profile:"
                               Margin="0,0,0,10"/>

                    <WrapPanel Orientation="Horizontal">
                        <Button Content="Add Serial Config" Command="{Binding AddSerialConfigurationCommand}"
                                Margin="0,0,10,5" Padding="10,5"/>
                        <Button Content="Add Network Config" Command="{Binding AddNetworkConfigurationCommand}"
                                Margin="0,0,10,5" Padding="10,5"/>
                        <Button Content="Add Link Layer Config" Command="{Binding AddLinkLayerConfigurationCommand}"
                                Margin="0,0,10,5" Padding="10,5"/>
                        <Button Content="Add Application Layer Config" Command="{Binding AddApplicationLayerConfigurationCommand}"
                                Margin="0,0,10,5" Padding="10,5"/>
                        <Button Content="Add Data Points" Command="{Binding AddDataPointsListCommand}"
                                Margin="0,0,10,5" Padding="10,5"/>
                    </WrapPanel>
                </StackPanel>
            </GroupBox>
        </StackPanel>
    </ScrollViewer>
</UserControl>
