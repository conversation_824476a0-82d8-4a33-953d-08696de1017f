using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;
using System.IO;
using System.Linq;
using DNP3Editor.Models;
using DNP3Editor.Services;
using DNP3Editor.Generated;
using DNP3Editor.Events;
using DNP3Editor.Utilities;
using DNP3Editor.Views;

namespace DNP3Editor.ViewModels;

public partial class SeparatedMainWindowViewModel : ObservableObject
{
    private readonly IXmlDocumentService _documentService;
    private readonly ILicenseService _licenseService;
    private readonly IValidationService _validationService;
    private readonly IFileDialogService _fileDialogService;
    private readonly IMessageBoxService _messageBoxService;
    private readonly ILogger<SeparatedMainWindowViewModel> _logger;

    [ObservableProperty]
    private string windowTitle = "DNP3 Device Profile Editor";

    [ObservableProperty]
    private string statusMessage = "Ready";

    [ObservableProperty]
    private Dnp3DeviceProfileDocument? currentDocument;

    [ObservableProperty]
    private string? currentFilePath;

    [ObservableProperty]
    private bool isDocumentModified;

    [ObservableProperty]
    private TabItemViewModel? selectedTab;

    [ObservableProperty]
    private bool isNavigationTreeVisible = true;

    [ObservableProperty]
    private bool isPropertiesPanelVisible = true;

    [ObservableProperty]
    private bool isOutputWindowVisible = true;

    public ObservableCollection<TabItemViewModel> OpenTabs { get; } = new();
    public ObservableCollection<RecentFileViewModel> RecentFiles { get; } = new();

    // Child ViewModels - injected or created as needed
    public TreeNavigationViewModel NavigationViewModel { get; private set; }
    public PropertiesPanelViewModel PropertiesViewModel { get; private set; }
    public OutputWindowViewModel OutputViewModel { get; private set; }
    public ValidationViewModel ValidationViewModel { get; private set; }

    public SeparatedMainWindowViewModel(
        IXmlDocumentService documentService,
        ILicenseService licenseService,
        IValidationService validationService,
        IFileDialogService fileDialogService,
        IMessageBoxService messageBoxService,
        TreeNavigationViewModel navigationViewModel,
        PropertiesPanelViewModel propertiesViewModel,
        OutputWindowViewModel outputViewModel,
        ValidationViewModel validationViewModel,
        ILogger<SeparatedMainWindowViewModel> logger)
    {
        _documentService = documentService;
        _licenseService = licenseService;
        _validationService = validationService;
        _fileDialogService = fileDialogService;
        _messageBoxService = messageBoxService;
        _logger = logger;

        NavigationViewModel = navigationViewModel;
        PropertiesViewModel = propertiesViewModel;
        OutputViewModel = outputViewModel;
        ValidationViewModel = validationViewModel;

        // Wire up events
        NavigationViewModel.NodeSelected += OnNavigationNodeSelected;
        ValidationViewModel.ValidationCompleted += OnValidationCompleted;
        
        // Initialize
        LoadRecentFiles();
        CheckLicense();
        UpdateWindowTitle();
    }

    #region Document Commands

    [RelayCommand]
    private async Task NewDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        try
        {
            var documentFactory = new DocumentFactory();
            CurrentDocument = documentFactory.CreateNewDocument();

            CurrentFilePath = null;
            IsDocumentModified = false;
            StatusMessage = "New document created";
            
            await LoadDocumentIntoViewModels();
            
            _logger.LogInformation("New document created");
        }
        catch (Exception ex)
        {
            await HandleError("Error creating new document", ex);
        }
    }

    [RelayCommand]
    private async Task OpenDocument()
    {
        if (!await ConfirmUnsavedChanges()) return;

        var filePath = await _fileDialogService.ShowOpenFileDialogAsync(
            "Open DNP3 Device Profile",
            "XML Files (*.xml)|*.xml|All Files (*.*)|*.*");

        if (!string.IsNullOrEmpty(filePath))
        {
            await LoadDocument(filePath);
        }
    }

    [RelayCommand]
    private async Task SaveDocument()
    {
        if (CurrentDocument == null) return;

        if (string.IsNullOrEmpty(CurrentFilePath))
        {
            await SaveAsDocument();
        }
        else
        {
            await SaveDocumentToFile(CurrentFilePath);
        }
    }

    [RelayCommand]
    private async Task SaveAsDocument()
    {
        if (CurrentDocument == null) return;

        var filePath = await _fileDialogService.ShowSaveFileDialogAsync(
            "Save DNP3 Device Profile",
            "XML Files (*.xml)|*.xml",
            "DeviceProfile.xml");

        if (!string.IsNullOrEmpty(filePath))
        {
            await SaveDocumentToFile(filePath);
        }
    }

    #endregion

    #region View Commands

    [RelayCommand]
    private async Task ValidateDocument()
    {
        if (CurrentDocument == null) return;
        await ValidationViewModel.ValidateDocumentAsync(CurrentDocument);
    }

    [RelayCommand]
    private async Task ShowDataPoints()
    {
        if (CurrentDocument?.ReferenceDevice?.DataPointsList == null)
        {
            await _messageBoxService.ShowWarningAsync("No Data Points", 
                "The current document does not contain data points.");
            return;
        }

        var tabManager = new TabManager(OpenTabs);
        var tab = await tabManager.GetOrCreateDataPointsTab(CurrentDocument.ReferenceDevice.DataPointsList);
        SelectedTab = tab;
    }

    [RelayCommand]
    private void CloseTab(TabItemViewModel? tab)
    {
        if (tab != null)
        {
            var tabManager = new TabManager(OpenTabs);
            tabManager.CloseTab(tab);
            
            if (SelectedTab == tab && OpenTabs.Count > 0)
            {
                SelectedTab = OpenTabs[0];
            }
        }
    }

    [RelayCommand]
    private async Task OpenRecent(string filePath)
    {
        if (!await ConfirmUnsavedChanges()) return;
        await LoadDocument(filePath);
    }

    #endregion

    #region Application Commands

    [RelayCommand]
    private void ShowPreferences()
    {
        // TODO: Show preferences dialog
        var preferencesViewModel = new PreferencesViewModel();
        // Show dialog...
    }

    [RelayCommand]
    private void ShowDocumentation()
    {
        // TODO: Open documentation
        DocumentationHelper.OpenUserGuide();
    }

    [RelayCommand]
    private void ShowAbout()
    {
        var aboutViewModel = new AboutViewModel();
        // Show dialog...
    }

    [RelayCommand]
    private async Task ExitApplication()
    {
        if (await ConfirmUnsavedChanges())
        {
            System.Windows.Application.Current.Shutdown();
        }
    }

    #endregion

    #region Private Methods

    private async Task LoadDocument(string filePath)
    {
        try
        {
            StatusMessage = "Loading document...";

            CurrentDocument = await _documentService.LoadDocumentAsync(filePath);
            CurrentFilePath = filePath;
            IsDocumentModified = false;

            await LoadDocumentIntoViewModels();
            AddToRecentFiles(filePath);

            StatusMessage = "Document loaded successfully";
            _logger.LogInformation("Document loaded from {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            await HandleError($"Error loading document from {filePath}", ex);
        }
    }

    private async Task SaveDocumentToFile(string filePath)
    {
        if (CurrentDocument == null) return;

        try
        {
            StatusMessage = "Saving document...";

            await _documentService.SaveDocumentAsync(CurrentDocument, filePath);

            CurrentFilePath = filePath;
            IsDocumentModified = false;
            StatusMessage = "Document saved successfully";

            AddToRecentFiles(filePath);
            _logger.LogInformation("Document saved to {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            await HandleError($"Error saving document to {filePath}", ex);
        }
    }

    private async Task LoadDocumentIntoViewModels()
    {
        if (CurrentDocument == null) return;

        NavigationViewModel.LoadDocument(CurrentDocument);

        await LoadTabsWithEventHandling(CurrentDocument);

        if (OpenTabs.Count > 0)
        {
            SelectedTab = OpenTabs[0];
        }
    }

    private async Task LoadTabsWithEventHandling(Dnp3DeviceProfileDocument document)
    {
        OpenTabs.Clear();

        if (document.ReferenceDevice?.Configuration != null)
        {
            var configTab = CreateDeviceConfigurationTabWithEvents(document.ReferenceDevice.Configuration, document.ReferenceDevice);
            OpenTabs.Add(configTab);
        }
    }

    private TabItemViewModel CreateDeviceConfigurationTabWithEvents(DnpConfigurationType configuration, Dnp3DeviceOptionalType parentDevice)
    {
        var viewModel = new DeviceConfigurationViewModel(configuration, parentDevice);

        // Wire up the event to mark document as modified
        viewModel.ConfigurationSaved += (sender, args) =>
        {
            IsDocumentModified = true;
            StatusMessage = "Device configuration saved";
        };

        // Wire up the event to refresh navigation tree when structure changes
        viewModel.ConfigurationStructureChanged += async (sender, args) =>
        {
            IsDocumentModified = true;
            StatusMessage = "Configuration sections added";

            // Refresh the navigation tree to show new sections
            if (CurrentDocument != null)
            {
                NavigationViewModel.LoadDocument(CurrentDocument);

                // Auto-select the newly created configuration section
                AutoSelectNewlyCreatedSection(args.SectionType);
            }
        };

        return new TabItemViewModel
        {
            Id = "configuration",
            Header = "Device Configuration",
            Content = new DeviceConfigurationView { DataContext = viewModel }
        };
    }

    private async Task<bool> ConfirmUnsavedChanges()
    {
        if (!IsDocumentModified) return true;

        var result = await _messageBoxService.ShowQuestionAsync(
            "Unsaved Changes",
            "The current document has unsaved changes. Do you want to save them?",
            MessageBoxButton.YesNoCancel);

        return result switch
        {
            MessageBoxResult.Yes => await TrySaveCurrentDocument(),
            MessageBoxResult.No => true,
            _ => false
        };
    }

    private async Task<bool> TrySaveCurrentDocument()
    {
        try
        {
            if (string.IsNullOrEmpty(CurrentFilePath))
            {
                await SaveAsDocument();
            }
            else
            {
                await SaveDocument();
            }
            return !IsDocumentModified;
        }
        catch
        {
            return false;
        }
    }

    private async Task HandleError(string message, Exception ex)
    {
        _logger.LogError(ex, message);
        await _messageBoxService.ShowErrorAsync("Error", $"{message}: {ex.Message}");
        StatusMessage = message;
    }

    private void OnNavigationNodeSelected(object? sender, NavigationNodeSelectedEventArgs e)
    {
        PropertiesViewModel.LoadProperties(e.SelectedNode);

        // Check if this node should open a tab
        HandleTabForSelectedNode(e.SelectedNode);
    }

    private void HandleTabForSelectedNode(NavigationNode selectedNode)
    {
        // Handle different node types that should have tabs
        switch (selectedNode.NodeType)
        {
            case NavigationNodeType.Configuration:
                OpenOrCreateTab("configuration", "Device Configuration", () => CreateDeviceConfigurationTab());
                break;

            case NavigationNodeType.Serial:
                OpenOrCreateTab("serial", "Serial Configuration", () => CreateSerialConfigurationTab(selectedNode));
                break;

            case NavigationNodeType.Network:
                OpenOrCreateTab("network", "Network Configuration", () => CreateNetworkConfigurationTab(selectedNode));
                break;

            case NavigationNodeType.Link:
                OpenOrCreateTab("link", "Link Layer Configuration", () => CreateLinkLayerConfigurationTab(selectedNode));
                break;

            case NavigationNodeType.DataPoints:
                OpenOrCreateTab("datapoints", "Data Points Manager", () => CreateDataPointsManagerTab(selectedNode));
                break;
        }
    }

    private void OpenOrCreateTab(string tabId, string tabHeader, Func<TabItemViewModel> createTabFunc)
    {
        // Check if tab already exists
        var existingTab = OpenTabs.FirstOrDefault(t => t.Id == tabId);
        if (existingTab != null)
        {
            // Tab exists, just activate it
            SelectedTab = existingTab;
        }
        else
        {
            // Tab doesn't exist, create it
            var newTab = createTabFunc();
            OpenTabs.Add(newTab);
            SelectedTab = newTab;
        }
    }

    private TabItemViewModel CreateDeviceConfigurationTab()
    {
        if (CurrentDocument?.ReferenceDevice?.Configuration != null)
        {
            return CreateDeviceConfigurationTabWithEvents(
                CurrentDocument.ReferenceDevice.Configuration,
                CurrentDocument.ReferenceDevice);
        }

        // Return empty tab if no configuration
        return new TabItemViewModel
        {
            Id = "configuration",
            Header = "Device Configuration",
            Content = new System.Windows.Controls.TextBlock { Text = "No configuration available" }
        };
    }

    private TabItemViewModel CreateSerialConfigurationTab(NavigationNode serialNode)
    {
        // Create a simple properties view for serial configuration
        var propertiesView = new Controls.PropertiesPanelControl();
        var propertiesViewModel = new PropertiesPanelViewModel();
        propertiesViewModel.LoadProperties(serialNode);
        propertiesView.DataContext = propertiesViewModel;

        return new TabItemViewModel
        {
            Id = "serial",
            Header = "Serial Configuration",
            Content = propertiesView
        };
    }

    private TabItemViewModel CreateNetworkConfigurationTab(NavigationNode networkNode)
    {
        // Create a simple properties view for network configuration
        var propertiesView = new Controls.PropertiesPanelControl();
        var propertiesViewModel = new PropertiesPanelViewModel();
        propertiesViewModel.LoadProperties(networkNode);
        propertiesView.DataContext = propertiesViewModel;

        return new TabItemViewModel
        {
            Id = "network",
            Header = "Network Configuration",
            Content = propertiesView
        };
    }

    private TabItemViewModel CreateLinkLayerConfigurationTab(NavigationNode linkNode)
    {
        // Create a simple properties view for link layer configuration
        var propertiesView = new Controls.PropertiesPanelControl();
        var propertiesViewModel = new PropertiesPanelViewModel();
        propertiesViewModel.LoadProperties(linkNode);
        propertiesView.DataContext = propertiesViewModel;

        return new TabItemViewModel
        {
            Id = "link",
            Header = "Link Layer Configuration",
            Content = propertiesView
        };
    }

    private TabItemViewModel CreateDataPointsManagerTab(NavigationNode dataPointsNode)
    {
        // Create a data points manager view
        var dataPointsView = new Views.DataPointsView();
        var dataPointsList = dataPointsNode.Data as DnpDataPointsListType ?? new DnpDataPointsListType();
        var dataPointsViewModel = new DataPointsViewModel(dataPointsList);
        dataPointsView.DataContext = dataPointsViewModel;

        return new TabItemViewModel
        {
            Id = "datapoints",
            Header = "Data Points Manager",
            Content = dataPointsView
        };
    }

    private void AutoSelectNewlyCreatedSection(ConfigurationSectionType sectionType)
    {
        // Find the configuration node
        var configNode = NavigationViewModel.RootNodes
            .FirstOrDefault()?.Children
            .FirstOrDefault(n => n.NodeType == NavigationNodeType.Configuration);

        if (configNode != null)
        {
            NavigationNode? nodeToSelect = null;

            // Find the specific node that was just created
            switch (sectionType)
            {
                case ConfigurationSectionType.Serial:
                    nodeToSelect = configNode.Children.FirstOrDefault(n => n.NodeType == NavigationNodeType.Serial);
                    break;
                case ConfigurationSectionType.Network:
                    nodeToSelect = configNode.Children.FirstOrDefault(n => n.NodeType == NavigationNodeType.Network);
                    break;
                case ConfigurationSectionType.LinkLayer:
                    nodeToSelect = configNode.Children.FirstOrDefault(n => n.NodeType == NavigationNodeType.Link);
                    break;
                case ConfigurationSectionType.DataPoints:
                    // Data points are at the root level, not under configuration
                    nodeToSelect = NavigationViewModel.RootNodes
                        .FirstOrDefault()?.Children
                        .FirstOrDefault(n => n.NodeType == NavigationNodeType.DataPoints);
                    break;
            }

            if (nodeToSelect != null)
            {
                NavigationViewModel.SelectNodeCommand.Execute(nodeToSelect);
            }
        }
    }

    private void OnValidationCompleted(object? sender, ValidationCompletedEventArgs e)
    {
        // Update UI based on validation results
        OutputViewModel.Clear();
        OutputViewModel.AddMessage("Validation Results:", MessageType.Info);

        if (e.Result.IsValid)
        {
            OutputViewModel.AddMessage("Document is valid!", MessageType.Success);
        }
        else
        {
            foreach (var error in e.Result.Errors)
            {
                OutputViewModel.AddMessage($"Error: {error}", MessageType.Error);
            }
        }
    }

    private void UpdateWindowTitle()
    {
        var title = "DNP3 Device Profile Editor";

        if (!string.IsNullOrEmpty(CurrentFilePath))
        {
            title = $"{Path.GetFileName(CurrentFilePath)} - {title}";
        }
        else if (CurrentDocument != null)
        {
            title = $"New Document - {title}";
        }

        if (IsDocumentModified)
        {
            title = $"*{title}";
        }

        WindowTitle = title;
    }

    private void LoadRecentFiles()
    {
        // TODO: Load from settings
        var recentFilesService = new RecentFilesService();
        var recentFiles = recentFilesService.GetRecentFiles();

        RecentFiles.Clear();
        foreach (var file in recentFiles)
        {
            RecentFiles.Add(file);
        }
    }

    private void AddToRecentFiles(string filePath)
    {
        var recentFilesService = new RecentFilesService();
        recentFilesService.AddRecentFile(filePath);
        LoadRecentFiles();
    }

    private void CheckLicense()
    {
        if (!_licenseService.IsLicenseValid())
        {
            // TODO: Show license dialog
            var licenseViewModel = new LicenseViewModel();
            // Show license dialog...
        }
    }

    #endregion

    #region Property Change Handlers

    partial void OnCurrentDocumentChanged(Dnp3DeviceProfileDocument? value)
    {
        UpdateWindowTitle();
    }

    partial void OnCurrentFilePathChanged(string? value)
    {
        UpdateWindowTitle();
    }

    partial void OnIsDocumentModifiedChanged(bool value)
    {
        UpdateWindowTitle();
    }

    #endregion
}
