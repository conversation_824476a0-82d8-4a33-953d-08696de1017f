{"Version": 1, "WorkspaceRootPath": "C:\\work-repos\\Forge\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|c:\\work-repos\\forge\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\work-repos\\Forge\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "C:\\work-repos\\Forge\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAAIAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T02:53:12.743Z", "EditorCaption": ""}]}]}]}