{"Version": 1, "WorkspaceRootPath": "C:\\work-repos\\Forge\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|c:\\work-repos\\forge\\code-behind-and-generation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|solutionrelative:code-behind-and-generation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|c:\\work-repos\\forge\\xml-document-service.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{54303C26-96DB-4551-76F8-A82A2BFFA0EC}|dnp3-editor.csproj|solutionrelative:xml-document-service.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "code-behind-and-generation.cs", "DocumentMoniker": "C:\\work-repos\\Forge\\code-behind-and-generation.cs", "RelativeDocumentMoniker": "code-behind-and-generation.cs", "ToolTip": "C:\\work-repos\\Forge\\code-behind-and-generation.cs", "RelativeToolTip": "code-behind-and-generation.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T11:15:38.975Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "xml-document-service.cs", "DocumentMoniker": "C:\\work-repos\\Forge\\xml-document-service.cs", "RelativeDocumentMoniker": "xml-document-service.cs", "ToolTip": "C:\\work-repos\\Forge\\xml-document-service.cs", "RelativeToolTip": "xml-document-service.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T11:15:16.613Z", "EditorCaption": ""}]}]}]}