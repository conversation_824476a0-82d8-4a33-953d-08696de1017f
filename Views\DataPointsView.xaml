<UserControl x:Class="DNP3Editor.Views.DataPointsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBarTray Grid.Row="0">
            <ToolBar>
                <ComboBox SelectedItem="{Binding SelectedDataPointType}" Width="150"
                          ItemsSource="{Binding DataPointTypes}"
                          DisplayMemberPath="DisplayName"
                          SelectedValuePath="Value">
                    <ComboBox.ItemContainerStyle>
                        <Style TargetType="ComboBoxItem">
                            <Setter Property="Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}"/>
                            <Setter Property="Background" Value="{DynamicResource SystemControlBackgroundAltHighBrush}"/>
                        </Style>
                    </ComboBox.ItemContainerStyle>
                </ComboBox>
                <Separator/>
                <Button Command="{Binding AddDataPointCommand}" ToolTip="Add Data Point">
                    <ui:SymbolIcon Symbol="Add"/>
                </Button>
                <Button Command="{Binding DeleteDataPointCommand}" ToolTip="Delete Data Point">
                    <ui:SymbolIcon Symbol="Delete"/>
                </Button>
                <Button Command="{Binding DuplicateDataPointCommand}" ToolTip="Duplicate Data Point">
                    <ui:SymbolIcon Symbol="Copy"/>
                </Button>
                <Separator/>
                <Button Command="{Binding ImportDataPointsCommand}" ToolTip="Import from CSV">
                    <ui:SymbolIcon Symbol="Download"/>
                </Button>
                <Button Command="{Binding ExportDataPointsCommand}" ToolTip="Export to CSV">
                    <ui:SymbolIcon Symbol="Upload"/>
                </Button>
            </ToolBar>
        </ToolBarTray>

        <!-- Data Points Grid -->
        <TabControl Grid.Row="1">
            <TabItem Header="Binary Inputs">
                <DataGrid ItemsSource="{Binding BinaryInputPoints}" 
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridComboBoxColumn Header="Event Class" 
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                        <DataGridTextColumn Header="State 0 Name" Binding="{Binding NameState0}" Width="100"/>
                        <DataGridTextColumn Header="State 1 Name" Binding="{Binding NameState1}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <TabItem Header="Analog Inputs">
                <DataGrid ItemsSource="{Binding AnalogInputPoints}" 
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="Units" Binding="{Binding Units}" Width="80"/>
                        <DataGridTextColumn Header="Scale Factor" Binding="{Binding ScaleFactor}" Width="100"/>
                        <DataGridTextColumn Header="Scale Offset" Binding="{Binding ScaleOffset}" Width="100"/>
                        <DataGridComboBoxColumn Header="Event Class" 
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <TabItem Header="Binary Outputs">
                <DataGrid ItemsSource="{Binding BinaryOutputPoints}"
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridComboBoxColumn Header="Event Class"
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                        <DataGridTextColumn Header="State 0 Name" Binding="{Binding NameState0}" Width="100"/>
                        <DataGridTextColumn Header="State 1 Name" Binding="{Binding NameState1}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <TabItem Header="Analog Outputs">
                <DataGrid ItemsSource="{Binding AnalogOutputPoints}"
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="Units" Binding="{Binding Units}" Width="80"/>
                        <DataGridTextColumn Header="Scale Factor" Binding="{Binding ScaleFactor}" Width="100"/>
                        <DataGridTextColumn Header="Scale Offset" Binding="{Binding ScaleOffset}" Width="100"/>
                        <DataGridComboBoxColumn Header="Event Class"
                                                SelectedItemBinding="{Binding ChangeEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>

            <TabItem Header="Counters">
                <DataGrid ItemsSource="{Binding CounterPoints}"
                          SelectedItem="{Binding SelectedDataPoint}"
                          AutoGenerateColumns="False" CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Index" Binding="{Binding Index}" Width="60"/>
                        <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="Roll Over" Binding="{Binding CounterRollOver}" Width="100"/>
                        <DataGridComboBoxColumn Header="Event Class"
                                                SelectedItemBinding="{Binding CounterEventClass}" Width="100">
                            <DataGridComboBoxColumn.ItemsSource>
                                <x:Array Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
                                    <sys:String>None</sys:String>
                                    <sys:String>One</sys:String>
                                    <sys:String>Two</sys:String>
                                    <sys:String>Three</sys:String>
                                </x:Array>
                            </DataGridComboBoxColumn.ItemsSource>
                        </DataGridComboBoxColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
