using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.ObjectModel;

namespace DNP3Editor.Models;

public partial class NavigationNode : ObservableObject
{
    [ObservableProperty]
    private string id = string.Empty;

    [ObservableProperty]
    private string name = string.Empty;

    [ObservableProperty]
    private string description = string.Empty;

    [ObservableProperty]
    private NavigationNodeType nodeType;

    [ObservableProperty]
    private object? data;

    [ObservableProperty]
    private bool isExpanded;

    [ObservableProperty]
    private bool isSelected;

    [ObservableProperty]
    private NavigationNode? parent;

    public ObservableCollection<NavigationNode> Children { get; } = [];

    public string Icon => NodeType switch
    {
        NavigationNodeType.Document => "Document",
        NavigationNodeType.Configuration => "Settings",
        NavigationNodeType.DataPoints => "List",
        NavigationNodeType.BinaryInput => "ToggleSwitch",
        NavigationNodeType.AnalogInput => "Slider",
        NavigationNodeType.Counter => "Calculator",
        NavigationNodeType.BinaryOutput => "Power",
        NavigationNodeType.AnalogOutput => "Volume",
        NavigationNodeType.Security => "Lock",
        NavigationNodeType.Network => "Globe",
        NavigationNodeType.Serial => "Cable",
        NavigationNodeType.Link => "Link",
        NavigationNodeType.Application => "Apps",
        _ => "Document"
    };
}

public enum NavigationNodeType
{
    Document,
    Configuration,
    DeviceInfo,
    Serial,
    Network,
    Link,
    Application,
    Master,
    Outstation,
    Security,
    DataPoints,
    BinaryInput,
    DoubleBitInput,
    BinaryOutput,
    AnalogInput,
    AnalogOutput,
    Counter,
    OctetString,
    VirtualTerminal,
    DataSets
}
