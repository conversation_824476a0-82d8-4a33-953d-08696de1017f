<UserControl x:Class="DNP3Editor.Controls.PropertiesPanelControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="5">
            <TextBlock Text="{Binding SelectedObjectName}" FontWeight="Bold" FontSize="14"/>
            <TextBlock Text="{Binding SelectedObjectType}" 
                       Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
        </StackPanel>

        <!-- Properties Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Properties}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="5,2">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Name}"
                                       VerticalAlignment="Center" FontWeight="SemiBold"/>
                            <TextBox Grid.Column="1" Text="{Binding Value}"
                                     IsReadOnly="{Binding IsReadOnly}"
                                     VerticalAlignment="Center"/>
                        </Grid>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>
