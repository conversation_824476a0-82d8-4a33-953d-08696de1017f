using System.ComponentModel;

namespace DNP3Editor.Services;

/// <summary>
/// Service to coordinate model change notifications between different ViewModels
/// that share the same underlying DNP3 model objects
/// </summary>
public class ModelChangeNotificationService
{
    private static readonly Lazy<ModelChangeNotificationService> _instance = new(() => new ModelChangeNotificationService());
    public static ModelChangeNotificationService Instance => _instance.Value;

    public event EventHandler<ModelChangedEventArgs>? ModelChanged;

    private ModelChangeNotificationService() { }

    /// <summary>
    /// Notify all subscribers that a model object has changed
    /// </summary>
    /// <param name="changedObject">The object that was modified</param>
    /// <param name="propertyName">The name of the property that changed</param>
    /// <param name="source">The source ViewModel that made the change</param>
    public void NotifyModelChanged(object changedObject, string? propertyName = null, object? source = null)
    {
        ModelChanged?.Invoke(this, new ModelChangedEventArgs(changedObject, propertyName, source));
    }

    /// <summary>
    /// Subscribe to model change notifications
    /// </summary>
    /// <param name="handler">The event handler to subscribe</param>
    public void Subscribe(EventHandler<ModelChangedEventArgs> handler)
    {
        ModelChanged += handler;
    }

    /// <summary>
    /// Unsubscribe from model change notifications
    /// </summary>
    /// <param name="handler">The event handler to unsubscribe</param>
    public void Unsubscribe(EventHandler<ModelChangedEventArgs> handler)
    {
        ModelChanged -= handler;
    }
}

/// <summary>
/// Event arguments for model change notifications
/// </summary>
public class ModelChangedEventArgs : EventArgs
{
    public object ChangedObject { get; }
    public string? PropertyName { get; }
    public object? Source { get; }

    public ModelChangedEventArgs(object changedObject, string? propertyName = null, object? source = null)
    {
        ChangedObject = changedObject;
        PropertyName = propertyName;
        Source = source;
    }
}
